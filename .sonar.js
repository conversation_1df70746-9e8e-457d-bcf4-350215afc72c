const scanner = require('sonarqube-scanner');
const pkg = require('./package.json');
const name = pkg.name.split('/').pop();
scanner(
    {
        serverUrl: 'http://**************:22001',
        token: '',
        options: {
            'sonar.login': 'jenkins',
            'sonar.password': 'cqrm12315',
            'sonar.projectName': name,
            'sonar.projectDescription': name,
            'sonar.sources': 'src',
            'sonar.sourceEncoding': 'UTF-8'
        },
    },
    () => process.exit(),
);
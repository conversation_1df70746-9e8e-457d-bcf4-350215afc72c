/*
 * @LastEditTime: 2022-11-07 16:28:33
 */
import routes from './src/routes';
import { defineConfig } from 'umi';
const ExtendSyntaxLessPlugin = require('@streamax/less-plugin-syntax-extend');
const LessFunctionOverrideLessPlugin = require('@streamax/less-plugin-dynamic-variable');

export default defineConfig({
    //     mock: {},
    // 约定式路由请删除routes配置
    routes: routes,
    devServer: { port: 9000 },
    webpack5: {},
    extraBabelPresets: [require.resolve('@streamax/babel-import-presets')],
    extraBabelPlugins: [
        ['import', { libraryName: '@streamax/poppy', style: true }, 'poppy'],
        [
            'import',
            {
                libraryName: '@arco-design/mobile-react',
                libraryDirectory: 'esm', // 注意如果是 SSR 环境，这里需使用 `cjs`
                style: (path) => `${path}/style`,
            },
            'arco',
        ],
    ],
    chainWebpack: (config) => {
        config.module
            .rule('mp4')
            .test(/\.(mp4|zip)(\?.*)?$/)
            .use('file-loader')
            .loader(require.resolve('file-loader'))
            .options({
                name: 'static/[name].[hash:8].[ext]',
                esModule: false,
            });
        const rule = config.module.rule('less').test(/\.(less)(\?.*)?$/);
        rule.oneOf('css')
            .use(require.resolve('@umijs/deps/compiled/less-loader'))
            .loader(require.resolve('@umijs/deps/compiled/less-loader'))
            .options({
                modifyVars: { 'root-entry-name': 'variable' },
                javascriptEnabled: true,
                plugins: [
                    new LessFunctionOverrideLessPlugin(),
                    new ExtendSyntaxLessPlugin(),
                ],
            });
        return config;
    },
});

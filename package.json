{"name": "base-login-web", "scope": "base", "version": "2.16.9-feature", "author": "quting", "scripts": {"start": "umi dev", "build": "umi build", "generate-deploy-scripts": "generate-deploy-scripts", "postinstall": "umi generate tmp", "lint": "npm run lint:js && npm run lint:prettier", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check '**/*.{js,jsx,tsx,ts,less,md,json}'", "prettier": "prettier -c --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "sonar": "node .sonar.js"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "dependencies": {"1.0.3": "^1.0.3", "@ahooksjs/use-url-state": "^3.5.1", "@arco-design/mobile-react": "^2.33.0", "@streamax/hooks": "^1.0.3", "@streamax/less-plugin-dynamic-variable": "1.0.1", "@streamax/less-plugin-syntax-extend": "1.0.3", "@streamax/poppy": "4.13.0", "@streamax/poppy-icons": "^4.1.25", "@streamax/runtime-starry-base": "2.2.0", "@streamax/umi-plugin-csp": "1.0.4", "axios": "^0.21.1", "file-loader": "^6.2.0", "html-loader": "^4.1.0", "i18next": "^20.2.1", "js-encrypt": "^2.3.4", "js-md5": "^0.7.3", "jspdf": "^2.5.1", "pdf-lib": "^1.17.1", "react-html-parser": "^2.0.2", "react-iframe": "^1.8.0", "react-transition-group": "^4.4.5", "thumbhash": "^0.1.1", "umi": "^3.4.7", "vconsole": "^3.15.1"}, "devDependencies": {"@commitlint/cli": "^17.2.0", "@streamax/babel-import-presets": "^1.0.5", "@streamax/deploy-scripts": "^2.2.0", "@streamax/lint": "^1.0.7", "@streamax/poppy-themes": "^1.0.1-alpha.3", "@streamax/umi-preset-base": "2.x", "@types/i18next": "^13.0.0", "@types/react": "^17.0.14", "@types/react-dom": "^17.0.9", "@umijs/test": "^3.2.28", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.2", "eslint": "^7.26.0", "lint-staged": "13.0.4", "pdfjs-dist": "2.5.207", "prettier": "^2.3.0", "sonarqube-scanner": "^2.8.1", "typescript": "^4.2.3", "yorkie": "^2.0.0"}, "resolutions": {"coa": "2.0.2", "node-downloader-helper": "1.0.19", "@geoman-io/leaflet-geoman-free": "2.10.0", "prettier-plugin-packagejson": "2.2.18"}}
apiVersion: "v1"
kind: "Component"
level: "base"
name: "base-login-web"
spec:
  dependencies_middleware_component:
    - name: "nginx"
      version: "V17.239.58"
  template_files:
    - "config.ini.j2"
    - "config.runtime.js.template.j2"
  cluster_variables:
    - name: web_public_port
      desc: 系统监听端口
      value: default(web_public_port, 20603)

    - name: static_intranet_addrs
      desc: 静态资源服务集群内网地址
      value: env(assets-engine,addresses)

    - name: static_public_url
      desc: 静态资源服务公网地址(https?://ip:port)
      value: default(static_public_url, /asset-engine)

    - name: gateway_public_url
      desc: 网关地址
      value: default(gateway_public_url, /gateway)

    - name: password_rsa2048_public_key
      desc: 密码加密公钥
      value: default(password_rsa2048_public_key, -----BEGIN%20PUBLIC%20KEY-----%0AMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0VIjjNNY93vCmjuGqTteFHdt5rDFtca5%0A6v9oTFXormfQRTgjPtsr5tzCvpThcC8eY4UgeJfQT3zGcDnaKAONJQKHD2Q2w12RraazY0WDZcro%0AoqoLGx8hImpuGLOxc9ttXFDftDskrmcKNb8c20d9tB1v8qVE%2FpwKQF%2F8dfzI7sbgGkSZkCfuXHJN%0AtDRt5%2B97i6scaCMtwma3N7dy3bPg6GYlqGPrzcBdYjiaFfHUK82pEKSo7xK8%2Fe80wtUvwjuRSMva%0AD%2BPcX%2FnoSdiGO4oN3JqPioGnwv6DjntktbudJ9iayK0sWl%2FDZ%2Fqf90GQwcpH6Snigo8LymZOFD4%2F%0A4BaFgwIDAQAB%0A-----END%20PUBLIC%20KEY-----)
      
    - name: find_password_areacode
      desc: 找回密码默认地区编码
      value: default(find_password_areacode, 86)

    - name: content_security_policy
      desc: 是否开启CSP安全策略
      value: default(content_security_policy , '0')

    - name: custom_login_bg_video_url
      desc: 登录页背景视频地址
      value: default(custom_login_bg_video_url, 0)

    - name: custom_login_bg_img_url
      desc: 登录页背景图片地址
      value: default(custom_login_bg_img_url, 0)

    - name: system_component_style
      desc: 海外风格开关
      value: default(system_component_style, 'default')
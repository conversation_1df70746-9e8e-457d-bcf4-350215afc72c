/**

 *<AUTHOR>

 *@date 2024/12/20 13:39:04

 *@description: add a description

 **/
import request from '@/utils/request';

// 调整第三方账户登录
export const bindThirdAccount = async (params?: any,config?: any) => {
    // @ts-ignore
    const data = await request({
        method: 'POST',
        url: `/base-server-service/api/v1/oauth/render/4login`,
        data: params,
        headers: {
            _langtype: window.localStorage.getItem('LOGIN_LANG'),
        },
        noDataInterceptor: config?.noDataInterceptor,
    });
    return data;
};

// 获取用户信息
export const getUserInfo = async (params?: any,headers?: any) => {
    // @ts-ignore
    const data = await request({
        method: 'get',
        url: `/base-server-service/api/v1/user/string/detail`,
        params,
        headers,
    });
    return data;
};

// 用户邮箱发送验证码
export const sendEmailToUser = async (params: any,headers?: any) => {
    // @ts-ignore
    const data = await request({
        method: 'POST',
        url: `/base-server-service/api/v1/email/verify`,
        data: params,
        headers
    });
    return data;
};

// 生产OTP邮件链接Key
export const generateOTPkey = async (params?: any) => {
    // @ts-ignore
    const data = await request({
        method: 'POST',
        url: `/base-server-service/api/v1/otp/email/key/generate`,
        data: params,
    });
    return data;
};




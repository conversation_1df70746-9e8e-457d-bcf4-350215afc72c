{"@base:@action__login.title": "Log in account", "@base:@action__login.button": "Log In", "@base:@message__login.required.message.username": "Please Enter A User Name", "@base:@message__login.username.chat.verification.message": "Invalid Character", "@base:@message__login.placeholder.username": "Please Enter A User Name", "@base:@message__login.required.message.password": "Please Enter Your Password", "@base:@message__login.placeholder.password": "Please Enter Your Password", "@base:@message__login.required.message.auth_code": "Please Enter The Verification Code", "@base:@message__login.min.message.auth_code": "The verification code contains at least four characters", "@base:@message__login.max.message.auth_code": "The verification code cannot exceed four characters", "@base:@message__login.format.message.auth_code": "The verification code format is incorrect.Please re-enter it", "@base:@message__login.placeholder.auth_code": "Please Enter The Verification Code", "@base:@message__login.placeholder.phone": "Please Enter Your Mobile Number", "@base:@message__login.phone.rule": "The format of mobile phone number is wrong.Please re-enter it", "@base:@message__login.phone.code.btn": "Obtaining The Verification Code", "@base:@message__login.phone.code.btn.countdown": "{count}s after", "@base:@message__login.type.account": "Account Password Login", "@base:@message__login.type.phone": "Mobile Phone Number Login", "@base:@message__login.max.message.phone.auth_code": "The verification code cannot exceed 6 characters", "@base:@message__login.security.verification": "Security Verification", "@base:@message__login.verification.failed.message": "Verification code error,please try again", "@base:@message__login.verification.failed.many.times.message": "Too many verification attempts, please try again", "@base:@message__login.verification.drag.message": "Drag the slider to complete the puzzle", "@base:@message__login.verification.image.failed.message": "Image acquisition failed,please try again", "@base:@action__update_password.title": "Change The Password", "@base:@message__update_password.placeholder.old_password": "Please Enter Your Old Password", "@base:@message__update_password.required.message.old_password": "Please Enter Your Old Password", "@base:@message__update_password.placeholder.new_password": "Please Enter A New Password", "@base:@message__update_password.required.message.new_password": "Please Enter A New Password", "@base:@message__update_password.required.message.password_length": "Fill in at least {count} characters", "@base:@message__update_password.required.message.password_length.max": "Fill in {count} characters at most", "@base:@message__update_password.placeholder.confirm_password": "Please Confirm The New Password", "@base:@message__update_password.required.message.confirm_password": "Please Confirm The New Password", "@base:@message__update_password.tips": "To ensure the security of your account,please update your password", "@base:@message__update_password.message.same_password": "The Two Passwords Are Different", "@base:@message__update_password.message.capital_password": "The password must contain uppercase and lowercase letters", "@base:@message__update_password.message.special_password": "The password must contain special characters", "@base:@message__update_password.message.same_as_old_password": "The new password must be different from the old one.Set a new password", "@base:@message__update_password.message.legal_greement": "Please check \"Agree\" before logging in", "@base:@message__update_password.title.legal_greement": "I Have Read And Agree", "@base:@message__update_password.title.no_legal_greement": "<PERSON><PERSON> is considered as agree", "@base:@message__update_password.title.and": "And", "@base:@message__update_password.title.legal": "User Agreement", "@base:@message__update_password.title.greement": "Privacy Policy", "@base:@action__update_password.confirm": "Confirm", "@base:@message__update_password.messagebox.success": "If the authentication expires,the system automatically exits.You need to verify the login again", "@base:@message__update_password.messagebox.title": "Prompt", "@base:@action__update_password.messagebox.confirm": "Confirm", "@base:@action__update_password.messagebox.login.go": "To Log In ({count})", "@base:@message__update_password.voucher.trans.token": "Signaling Conversion Failure", "@base:@message__update_password.message.success.tip": "The password is successfully changed.After {count} seconds,the login page is displayed", "@base:@message__update_password.message.new_password.sameto.old_password": "The new password must be different from the old one.Set a new password", "@base:@name__document_title_password.title.legal": "User Agreement", "@base:@name__document_title_password.title.greement": "Privacy Policy", "@base:@action__forget_password.title": "Forgot Password", "@base:@name__find_password.title": "Retrieve Password", "@base:@action__find_password.next.step": "Next", "@base:@message__reset_password.input.account": "Please enter the account for which you need to reset your password", "@base:@message__reset_password.check.account.exist": "The user does not exist,please re-enter", "@base:@message__reset_password.identity.confirm": "To confirm that it was your own operation,please perform identity verification", "@base:@message__login.check.trust.tip": "Trust this device, skip verification for 7 days. (Do not check on public computers)", "@base:@message__reset_password.no.verification.code": "Unable to receive verification code,please contact the administrator", "@base:@message__reset_password.placeholder.email": "Please Enter Your Email Address", "@base:@message__reset_password.placeholder.input.password.again": "Please enter the new password again", "@base:@name__reset_password.title": "Reset Password", "@base:@message__reset_password.success": "Password Reset Successful", "@base:@action__reset_password.login.right.now": "Jump Now", "@base:@action__reset_password.redirect.login.seconds": "Jump to login page in seconds", "@base:@message__reset_password.find.way.card.context.phone": "Obtain SMS verification code through {phoneNumber}", "@base:@message__reset_password.find.way.card.context.email": "Obtain email verification code through {mailNumber}", "@base:@message__reset_password.input.complete.phone": "Please enter the phone number bound to the account", "@base:@message__reset_password.input.complete.email": "Please enter the email address bound to the account", "@base:@message__reset_password.use.to.identify": "To Verify Identity", "@base:@message__reset_password.no.bind.email": "No email is currently bound.Please contact the administrator", "@base:@message__reset_password.no.bind.phone": "No phone number has been bound yet.Please contact the administrator", "@base:@message__reset_password.input.right.phone": "Please enter the correct phone number", "@base:@message__reset_password.input.right.email": "Please Enter The Correct Email", "@base:@message__reset_password.email.format.error": "Email format error,please re-enter", "@base:@message__reset_password.auth.code.error": "Verification Code Error", "@base:@message__reset_password.process.end.message": "The process of retrieving the password has ended.Jump to the login page in {second} seconds", "@base:@name__second_validate.title": "Login Security Verification", "@base:@name__second_validate.pc.title": "Two-Step Verification", "@base:@name__second_validate.tip.message": "Your account may pose security risks.To ensure personal operation,please select your email or phone number for security verification", "@base:@name__second_validate.pc.tip.message": "To protect your account security, please complete two-step verification.", "@base:@name__second_validate.way.label": "Authentication", "@base:@name__second_validate.way.label.email": "Email Verification", "@base:@name__second_validate.way.label.phone": "Mobile Verification", "@base:@name__second_validate.way.label.OTP": "OTP Verification", "@base:@message__second_validate.choose.way.message": "Please Select The Verification Method", "@base:@message__second_validate.safety.validate": "Security Verification", "@base:@message__second_validate.safety.success": "Verification Passed", "@base:@message__second_validate.safety.user.email": "Please contact the administrator to set the user email", "@base:@message__second_validate.safety.user.phone": "Please contact the administrator to set the user phone number", "@base:@message__second_validate.safety.validate.success": "Security verification passed,login successful", "@base:@message__second_validate.otp.validate.empty": "Please enter the dynamic password", "@base:@message__second_validate.otp.validate.error": "Invalid dynamic password，please try again", "@base:@message__second_validate.otp.validate.title": "For added security, please enter the One Time Password (OTP) associated with your account.", "@base:@message__second_validate.otp.placeholder": "OTP", "@base:@message__second_validate.otp.tips.not.binding": "If you have not completed OTP binding, please ", "@base:@message__second_validate.otp.tips.click.here": "Click here ", "@base:@message__second_validate.otp.tips.binding": "to enter OTP Binding Process", "@base:@message__second_validate.otp.tips.not.binding.email": "No email address is linked to your account. Please contact the administrator for assistance.", "@base:@message__second_validate.otp.validate.modal.title": "Confirmation", "@base:@message__second_validate.otp.validate.modal.info": "After confirming the operation, the system will send an email containing the OTP binding link to the email address below.", "@base:@message__user_phone.input.length.tips": "Please Enter 6-15 Characters", "@base:@message__user_not.get.validate.code": "Please obtain the verification code first", "@base:@message__reset_password.validate.ways.phone": "Phone Numer Verification", "@base:@message__get_validate.code.times.limit": "The verification code has exceeded the number of times limit.To ensure account security,please obtain it again after 24 hours", "@base:@message__get_auth.code.fail.send.sms": "SMS Verification Sending Failed", "@base:@message__get_repeat.code.fail.send.email.repeat": "Sending failed, duplicate user email", "@base:@message__get_repeat.code.fail.send.phone.repeat": "Sending failed, user phone number repeat", "@base:@return__120020030": "The number of Captcha errors is too many,please get it again", "@base:@message__common.activating": "Activating, please wait", "@base:@action__login.active.point.title": "Activate Branch Point", "@base:@action__login.active.tenant.title": "Activate Tenant", "@base:@action__login.active.point.success": "Activate branch point successfully", "@base:@action__login.active.point.label.license": "Branch Point License", "@base:@message__login.active.point.required.message.username": "Branch point license cannot be empty", "@base:@action__login.active.point.placeholder.license": "Please enter branch point License", "@base:@action__login.active.point.label.pointConfigFileId": "Branch Point Configuration Package", "@base:@message__login.required.message.pointConfigFileId": "Please upload branch point configuration file", "@base:@action__login.active.point.label.resoureFileIdList": "Application Resource Package", "@base:@message__login.required.message.resoureFileIdList": "Please upload application resource package", "@base:@message__common.uploading": "Uploading", "@base:@action__login.active.tenant.success": "Activate tenant successfully", "@base:@action__login.active.tenant.label.tenantLicense": "Tenant License", "@base:@message__login.active.tenant.required.message.tenantLicense": "Tenant license cannot be empty", "@base:@action__login.active.tenant.placeholder.tenantLicense": "Please enter tenant license", "@base:@action__login.active.tenant.label.tanantConfigFileId": "Tenant Configuration Package", "@base:@message__login.required.message.tanantConfigFileId": "Please upload tenant configuration package", "@base:@message__upload.file.type.error": "Only supports uploading {accept} format files", "@base:@action__upload.file.button": "Upload File", "@base:@message__upload.file.type.tips": "Only supports extensions", "@base:@action__login.active.modal.ok": "Confirm", "@base:@action__login.active.modal.cancel": "Cancel", "@base:@message__upload.file.size.error": "Only supports a maximum upload of {size}M", "@base:@return__tenant_expire_fail": "The tenant has expired. Contact the administrator", "@base:@return__tenant_expire_fail.renewal": "The tenant has expired, please contact the administrator or {renewal}", "@base:@message__tenant.expired.message.manual.renewal": "Manual Renewal", "@base:@message__tenant.renewal.modal.title": "Tenant <PERSON>", "@base:@message__tenant.renewal.modal.tenant.name": "Tenant <PERSON>", "@base:@message__tenant.renewal.modal.tenant.id": "Tenant ID", "@base:@message__tenant.renewal.modal.renewal.success": "Tenant renewal successful", "@base:@action_login.interface.center.title": "Interface center", "@base:@message_login.legalAgreement.mobile.title": "Legal disclaimer and agreement policy", "@base:@action__bind_account.title": "Account binding", "@base:@action__bind_account.confirm": "Binding", "@base:@message__login.type.microsoft": "Microsoft account login"}
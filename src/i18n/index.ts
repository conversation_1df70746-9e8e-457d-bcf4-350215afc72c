// @ts-nocheck
import React from 'react';
import i18next, { I18nFormatModule } from 'i18next';

const appLocale = {};

export const I18nFormater: I18nFormatModule = {
    type: 'i18nFormat',
    parse(res, options, lng, ns, key) {
        if (!res) return key;
        if (!options) return res;

        const result = res.split(/(\{.+?\})/g).map((item) => {
            if (/(\{.+?\})/.test(item)) {
                const t = item.replace(/\{(.+?)\}/, '$1');
                return options[t] || item;
            }
            return item;
        });

        if (result.some((item) => React.isValidElement(item))) {
            return React.Children.toArray(result);
        }

        return result.join('');
    },
};
export const I18nInit = async (lng, data) => {
    lng = lng.replace('_', '-');
    /* eslint-disable */
    try {
        await i18next.use(I18nFormater).init({
            // debug: process.env.NODE_ENV === 'development',
            keySeparator: false,
            nsSeparator: false,
            lng,
            // 一次只加载一种
            resources: {
                [lng]: {
                    translation: { ...appLocale, ...data },
                },
            },
        });
    } catch (e) {
        throw e;
    }
};

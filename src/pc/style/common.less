@main-color: #597ef7;
@main-color-hover: #85a5ff;
@main-color-active: #415ed1;

.login-page-select-dropdown,
.login-page-select-phone-dropdown {
    top: 0px !important;
    left: 0px !important;
    width: 440px !important;
    min-width: 440px !important;

    .poppy-select-item-option--active,
    .poppy-select-item-option-selected {
        //color: #fff;
        background: #cbddfd !important;
    }
}
.login-page-select-phone-dropdown {
    top: 368px !important;
}
.poppy-tooltip {
    .poppy-tooltip-content {
        .poppy-tooltip-inner {
            word-break: break-all;
        }
    }
}
.poppy-input-affix-wrapper {
    border-radius: 2px !important;
    &::abroad {
        border-radius: 8px !important;
    }
}

.poppy-input-affix-wrapper,
.poppy-select-selector {
    font-weight: 400;
    font-size: 16px;
    font-family: SourceHanSansCN-Regular, sans-serif;
    letter-spacing: 0;
    // background: rgba(255, 255, 255, 0.15) !important;
    border-radius: 2px;
    outline: none;
    &:focus {
    }
    .poppy-input {
        font-weight: 400;
        font-size: 16px;
        font-family: SourceHanSansCN-Regular, sans-serif;
        letter-spacing: 0;
        background: transparent;
        &::placeholder {
        }
    }
}

.poppy-select-selector {
    height: 40px !important;
    padding: 4px 0 0 11px !important;
    &::abroad {
        height: 56px !important;
        padding-top: unset !important;
    }
}
.poppy-form-item-control-input-content {
    & > * {
        height: 40px;
        &::abroad {
            height: 56px;
        }
    }
}
.poppy-input-affix-wrapper-focused,
.poppy-select {
    outline: none;
}
.poppy-form-item {
    .poppy-form-item-control-input-content {
        .poppy-input {
            color: rgba(0, 0, 0, 0.85);
            font-size: 16px;
        }
    }
}

.poppy-input:hover {
    border-color: @main-color;
}
.poppy-input:focus,
.poppy-input-focused {
    border-color: @main-color;
}
.poppy-select {
    position: relative;
}
.poppy-select:not(.poppy-select-disabled):hover .poppy-select-selector {
    border-color: @main-color;
}
// 下拉框hover focus颜色
.login-content-form .poppy-select-selector:focus {
    border-color: @main-color;
}
.poppy-select-selector:focus,
.poppy-select-selector:focus-within {
    border: 1px solid @main-color !important;
}
.poppy-select-item {
    line-height: 26px !important;
}
// 复选框选中颜色，hover颜色
// 复选框选中颜色，hover颜色
.poppy-checkbox-checked .poppy-checkbox-inner {
    background-color: @main-color;
    border-color: @main-color;
}

.poppy-checkbox-wrapper:hover .poppy-checkbox-inner,
.poppy-checkbox:hover .poppy-checkbox-inner,
.poppy-checkbox-input:focus + .poppy-checkbox-inner {
    border-color: @main-color !important;
}
.input-prefix {
    margin-right: 15px;
    padding-right: 15px;
    font-size: 16px;
    border-right: 1px solid rgba(255, 255, 255, 0.8);
}
.poppy-btn-primary {
    background-color: @main-color;
}
.poppy-btn-primary:hover,
.poppy-btn-primary:focus {
    background-color: @main-color-hover;
}
.poppy-btn-primary:active {
    background-color: @main-color-active;
}
.poppy-input:-webkit-autofill,
.poppy-input[type='text']:focus .poppy-input[type='password']:focus {
    -webkit-box-shadow: 0 0 0 1000px transparent inset; // 背景设为白色
    transition: background-color 5000000s ease-in-out 0.3s;
    // -webkit-text-fill-color: rgba(255, 255, 255, 0.45); // 字体颜色
}

.login-page-title() {
    &::abroad {
        font-weight: 600;
        font-size: 28px;
    }
}

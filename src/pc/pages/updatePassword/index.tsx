// @ts-nocheck
import { useState, useEffect, useRef, useMemo } from 'react';
import { history } from 'umi';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { Button, Form, Input, message } from '@streamax/poppy';
import classNames from 'classnames';
import {
    encryptors,
    getScale,
    getRuntimeLocales,
    queryLoginConfig,
    getOffsetInfo,
} from '@/utils/common';
import { LOGINSTYLE1, LOGIN_STYLE7 } from '../../../utils/constant';
// @ts-ignore
import md5 from 'js-md5';
// import LoginBackImg from '@/component/LoginBackImg';
import usePasswordConfig from '@/hooks/usePasswordConfig';
import { IconTipsFill } from '@streamax/poppy-icons';
import AnimationBg from '../../component/AnimationBg';
import './index.less';
import { useAsyncEffect } from '@streamax/hooks';
import LoginFormBg, { isRightLoginStyle } from '@/pc/component/LoginFormBg';
import BlurImg from '@/pc/component/BlurImg';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';

const defaultLocales = require('../../../app.locale.json');
// RSA 2048 加密
const encryptor = encryptors();

// todo 提取登录和修改密码的公共方法
export default () => {
    const [scale, setScale] = useState(getScale());

    const [form] = Form.useForm();

    const [errInfo, setErrInfo] = useState();

    const [loading, setLoading] = useState(false);

    const [state, setState] = useState(false);

    const [config, setConfig] = useState<any>(null);

    const [illustrationUrl, setIllustrationUrl] = useState<string>(''); // 当前使用的插图url
    const [illustrationHash, setIllustrationHash] = useState<string>('');

    const [illustrationStyle, setIllustrationStyle] = useState<boolean>(false); // 是否使用插图模式

    const [bgImageUrl, setBgImageUrl] = useState<string>('');
    const [bgImageHash, setBgImageHash] = useState<string>('');
    const [backGroundLoaded, setBackGroundLoaded] = useState<boolean>(false);

    const { pwdRules } = usePasswordConfig();
    // const confirmDomRef = useRef();
    // const timerRef = useRef();
    const timeOutRef = useRef();
    useEffect(() => {
        window.addEventListener('resize', () => {
            setScale(getScale());
        });
        form.resetFields([
            'account',
            'oldPassword',
            'newPassword',
            'confirmPassword',
        ]);
        form.setFieldsValue({
            account: history.location.query.account,
        });
    }, []);
    // 获取国际化词条
    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType) return;
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        setState(!state);
        // eslint-disable-next-line no-plusplus
    };
    // 获取登录策略
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        try {
            dataConfig = await queryLoginConfig();
            setConfig(dataConfig);
            if (dataConfig?.curUseImageId) {
                const hash = (dataConfig.images || []).find(
                    (item) => item.imageId == dataConfig?.curUseImageId,
                )?.hash;
                setBgImageHash(hash);
                setBgImageUrl(dataConfig?.curUseImageUrl);
            }
            if (dataConfig?.curUseIllustrationId) {
                const hash = (dataConfig.illustration || []).find(
                    (item) => item.imageId == dataConfig?.curUseIllustrationId,
                )?.hash;
                setIllustrationHash(hash);
                setIllustrationUrl(dataConfig?.curUseIllustrationUrl);
            }
            setIllustrationStyle(dataConfig?.loginIllustration || false);
            setBackGroundLoaded(true);
        } catch (e) {
            setConfig({});
            setBackGroundLoaded(true);
        }
        return dataConfig;
    };

    // 计算登录框的左侧的位置只能左侧移动定位，右侧定位不生效
    const calculateLeftLocationDebounce = useMemo(() => {
        // 判断是否右侧布局，不是就返回0
        if (!isRightLoginStyle(config?.loginPageStyle)) return 0;
        const { width } = getOffsetInfo(document.body);
        const leftMax = width - 800 * scale > 0 ? width - 800 * scale : 0;
        return leftMax;
    }, [scale, config?.loginPageStyle]);

    useAsyncEffect(async () => {
        const lng = window.localStorage.getItem('LOGIN_LANG') || 'en_US';
        const loginConfig = await getLoginConfig();
        await i18nInit(lng as string, loginConfig?.tenantId);
        document.title = i18n.t('@base:@action__update_password.title');
    }, []);

    const validatorNewPassword = ({ getFieldValue }) => ({
        validator(_: any, value: any) {
            if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
            }
            return Promise.reject(
                i18n.t('@base:@message__update_password.message.same_password'),
            );
        },
    });
    // 对比行密码不能和旧密码相同
    const validatorSameToOldpassword = ({ getFieldValue }) => ({
        validator(_: any, value: any) {
            if (
                !value ||
                !getFieldValue('oldPassword') ||
                getFieldValue('oldPassword') !== value
            ) {
                return Promise.resolve();
            }
            return Promise.reject(
                i18n.t(
                    '@base:@message__update_password.message.new_password.sameto.old_password',
                ),
            );
        },
    });
    const formValueChanged = (values) => {
        const inputKey = Object.keys(values)[0];
        const { confirmPassword, newPassword } = form.getFieldsValue();
        if (inputKey === 'newPassword' && confirmPassword) {
            form.validateFields(['confirmPassword']);
        } else if (inputKey === 'oldPassword' && newPassword) {
            form.validateFields(['newPassword']);
        }
    };
    // 返回登录
    const logout = async () => {
        clearTimeout(timeOutRef.current);
        const loginUrl = location.origin + '/login/pc/login';
        const redirect_url =
            '?redirect_url=' + history.location.query.redirect_url;
        const callbackUrl = `${loginUrl}${
            history.location.query.redirect_url ? redirect_url : ''
        }`;
        window.location.href = callbackUrl;
    };
    // 更新密码
    const handleUpdatePassword = async () => {
        form.validateFields().then(async (values: any) => {
            setLoading(true);
            request({
                method: 'post',
                url: '/base-server-service/api/v1/user/pwd',
                noDataInterceptor: true,
                data: {
                    newPwd: encryptor.encrypt(md5(values.newPassword)),
                    oldPwd: encryptor.encrypt(md5(values.oldPassword)),
                    userId: history.location.query.userId,
                },
                headers: {
                    _token: window.localStorage.getItem(
                        'UPDATE_PASSWORD_TOKEN',
                    ),
                },
            })
                .then((rs: any) => {
                    const { data, config } = rs;
                    if (data.code === 200 || data.code === '200') {
                        setLoading(false);
                        message.success(
                            i18n.t(
                                '@base:@message__update_password.message.success.tip',
                                {
                                    count: 3,
                                },
                            ),
                        );
                        timeOutRef.current = setTimeout(logout, 3000);
                    } else if (
                        data.code == 1403 ||
                        data.code == 1407 ||
                        data.code == 1458
                    ) {
                        setLoading(false);
                        setErrInfo(
                            tt(data.langKey, '', {
                                count: (data['errorVar'] || [])[0],
                            }),
                        );
                    } else {
                        timeOutRef.current = setTimeout(logout, 3000);
                        setLoading(false);
                        setErrInfo(
                            tt(data.langKey, '', {
                                count: (data['errorVar'] || [])[0],
                            }),
                        );
                    }
                })
                .catch((err: any) => {
                    setLoading(false);
                    setErrInfo(err);
                });
        });
    };

    if (
        config === null ||
        window.langueCache[
            window.localStorage.getItem('LOGIN_LANG') || 'en_US'
        ] === 'loading'
    )
        return null;

    const renderContent = () => {
        return (
            <div
                className={classNames('update-password-content-form', {
                    'login-style7': config?.loginPageStyle === LOGIN_STYLE7,
                })}
            >   
                <div className="update-password-form-title">
                    {tt('@base:@action__update_password.title')}
                </div>
                <div className='scroll-wrap'>
                    <div className="update-password-form-tips">
                        <IconTipsFill />
                        <span>{tt('@base:@message__update_password.tips')}</span>
                    </div>
                    <Form
                        form={form}
                        autoComplete="off"
                        onValuesChange={formValueChanged}
                        onKeyPress={(e) => {
                            if (e.nativeEvent && e.nativeEvent.code === 'Enter') {
                                handleUpdatePassword();
                            }
                        }}
                    >
                        <AFormItem
                            name="account"
                            rules={[
                                {
                                    required: true,
                                },
                            ]}
                        >
                            <Input disabled />
                        </AFormItem>

                        <AFormItem
                            name="oldPassword"
                            rules={[
                                {
                                    required: true,
                                    message: tt(
                                        '@base:@message__update_password.required.message.old_password',
                                    ),
                                },
                            ]}
                        >
                            <Input
                                type="password"
                                autoComplete="new-password"
                                placeholder={i18n.t(
                                    '@base:@message__update_password.placeholder.old_password',
                                )}
                                maxLength={20}
                            />
                        </AFormItem>

                        <AFormItem
                            name="newPassword"
                            validateFirst
                            rules={[
                                {
                                    required: true,
                                    message: tt(
                                        '@base:@message__update_password.required.message.new_password',
                                    ),
                                },
                                ...pwdRules,
                                validatorSameToOldpassword,
                            ]}
                        >
                            <Input
                                type="password"
                                autoComplete="new-password"
                                placeholder={i18n.t(
                                    '@base:@message__update_password.placeholder.new_password',
                                )}
                                maxLength={20}
                            />
                        </AFormItem>

                        <AFormItem
                            name="confirmPassword"
                            rules={[
                                {
                                    required: true,
                                    message: tt(
                                        '@base:@message__update_password.required.message.confirm_password',
                                    ),
                                },
                                validatorNewPassword,
                            ]}
                        >
                            <Input
                                type="password"
                                autoComplete="new-password"
                                placeholder={i18n.t(
                                    '@base:@message__update_password.placeholder.confirm_password',
                                )}
                                maxLength={20}
                            />
                        </AFormItem>
                    </Form>
                </div>
                <Button
                    loading={loading}
                    className="update-btn"
                    type="primary"
                    onClick={handleUpdatePassword}
                >
                    {tt('@base:@action__update_password.confirm')}
                </Button>

                <div className="error-info">{errInfo}</div>
            </div>
        );
    };

    return (
        <div id="update-password-container" className="update-password">
            {backGroundLoaded ? (
                <div
                    className="update-password-video-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                    }}
                >
                    <AnimationBg
                        bgImageUrl={bgImageUrl}
                        bgImageHash={bgImageHash}
                        loginPageStyle={config?.loginPageStyle}
                    />
                </div>
            ) : null}

            {backGroundLoaded ? (
                <div
                    className="update-password-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {!illustrationStyle ? (
                        <LoginFormBg loginPageStyle={config?.loginPageStyle} />
                    ) : null}
                </div>
            ) : null}
            {/* {backGroundLoaded ? (
                <>
                    <AnimationBg bgImageUrl={bgImageUrl} />
                    <LoginFormBg />
                </>
            ) : null} */}

            {/* <LoginBackImg bgImageUrl={bgImageUrl}/> */}

            {backGroundLoaded &&
            (illustrationStyle ? illustrationUrl : true) ? (
                <div
                    className="update-password-wrapper"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {illustrationStyle &&
                    config.loginPageStyle === LOGINSTYLE1 ? (
                        <div className="illustration-login-style">
                            <div className="illustration">
                                <BlurImg
                                    img={{
                                        url: illustrationUrl,
                                        hash: illustrationHash,
                                    }}
                                />
                            </div>
                            <div className="update-password-content login-new-style1">
                                {renderContent()}
                            </div>
                        </div>
                    ) : (
                        <div className="update-password-content">
                            {renderContent()}
                        </div>
                    )}
                </div>
            ) : null}
        </div>
    );
};

/*
 * @Author: yxfan
 * @Date: 2024-06-18 11:17:33
 * @LastEditTime: 2024-06-19 17:20:33
 * @Description: 登录页的局点配置
 */
import { Tooltip, message, Spin } from '@streamax/poppy';
import { useState, useEffect, useRef, useMemo } from 'react';
import { isRightLoginStyle } from '@/pc/component/LoginFormBg';
import { IconAuthority01Fill, IconUser01Fill } from '@streamax/poppy-icons';
import ActivePointModal from './ActivePointModal';
import ActiveTenantModal from './ActiveTenantModal';
import request from '@/utils/request';

import './index.less';
import { tt } from '@/utils/i18n';

/** @Description UI设计稿上的边距 */
const DISTANCE_DESIGN = 64;

enum ACTIVE_NUM {
    'unactivate' = 0,
    'activated' = 1,
    'stop' = 2,
    'illegal' = 3,
}

type ActiveStatusRes = {
    pointActiveStatus: ACTIVE_NUM;
    tenantActiveLimit: boolean;
    pointActivating: boolean;
};

interface Props {
    scale: number;
    config?: {
        loginPageStyle: number;
    };
}
export default (props: Props) => {
    const { scale, config } = props;
    const [activePointVis, setActivePointVis] = useState(false);
    const [activeTenantVis, setActiveTenantVis] = useState(false);
    const [statusInfo, setStatusInfo] = useState<ActiveStatusRes>();
    const { pointActiveStatus, tenantActiveLimit, pointActivating } =
        statusInfo || {};
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        queryActiveStatus();
    }, []);

    async function queryActiveStatus() {
        try {
            const data = await request({
                url: '/base-server-service/api/v1/license/point/active/status',
                method: 'get',
            });
            // @ts-ignore
            setStatusInfo(data);
            return data;
        } catch (e) {
            message.error(e);
            return Promise.reject();
        }
    }

    const style = useMemo(() => {
        const distance = DISTANCE_DESIGN * scale;
        if (config) {
            if (isRightLoginStyle(config?.loginPageStyle)) {
                return { bottom: distance, left: distance };
            }
            return { bottom: distance, right: distance };
        } else {
            return {};
        }
    }, [scale, config?.loginPageStyle]);

    async function handleOperate(type: string) {
        if (type === 'activePoint') {
            setLoading(true);
            try {
                const { pointActivating } = (await queryActiveStatus()) as any;
                if (pointActivating) {
                    message.warn(
                        tt(
                            '@base:@message__common.activating',
                            '正在激活中，请等待',
                        ),
                    );
                    return;
                }
                setActivePointVis(true);
            } finally {
                setLoading(false);
            }
        }
        if (type === 'activeTenant') {
            setActiveTenantVis(true);
        }
    }

    function renderActivePoint() {
        return (
            <Tooltip
                title={tt(
                    '@base:@action__login.active.point.title',
                    '激活局点',
                )}
            >
                <div
                    className="active-point"
                    onClick={() => handleOperate('activePoint')}
                >
                    <IconAuthority01Fill className="icon" />
                </div>
            </Tooltip>
        );
    }
    function renderActiveTenant() {
        return (
            <Tooltip
                title={tt(
                    '@base:@action__login.active.tenant.title',
                    '激活租户',
                )}
            >
                <div
                    className="active-point"
                    onClick={() => handleOperate('activeTenant')}
                >
                    <IconUser01Fill className="icon" />
                </div>
            </Tooltip>
        );
    }
    function judgeStatusRender() {
        if ([ACTIVE_NUM.unactivate].includes(pointActiveStatus)) {
            return renderActivePoint();
        }
        if (
            tenantActiveLimit === false &&
            pointActiveStatus == ACTIVE_NUM.activated
        ) {
            return renderActiveTenant();
        }
    }

    if (
        // 没有局点数据
        pointActiveStatus === undefined ||
        // 没有租户数据
        tenantActiveLimit === undefined ||
        // 局点已激活，租户达到上限
        (pointActiveStatus === ACTIVE_NUM.activated && tenantActiveLimit)
    ) {
        return null;
    }

    return (
        <div
            className="point-config-container"
            style={{ ...style, transform: `scale(${scale})` }}
        >
            {loading ? <Spin spinning={loading} /> : judgeStatusRender()}
            {activePointVis && (
                <ActivePointModal
                    close={() => setActivePointVis(false)}
                    refresh={queryActiveStatus}
                />
            )}
            {activeTenantVis && (
                <ActiveTenantModal
                    close={() => setActiveTenantVis(false)}
                    refresh={queryActiveStatus}
                />
            )}
        </div>
    );
};

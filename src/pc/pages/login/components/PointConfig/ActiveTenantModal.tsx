/*
 * @Author: yxfan
 * @Date: 2024-06-18 14:36:26
 * @LastEditTime: 2024-09-23 13:24:28
 * @Description: 激活租户
 */
import { useState, useRef } from 'react';
import { Modal, Spin, Form, Input, Button, message } from '@streamax/poppy';
import Upload, { UploadRef } from '@/pc/component/Upload';
import { tt } from '@/utils/i18n';
import request from '@/utils/request';
import i18n from 'i18next';
import {
    ErrorInfos,
    getI18KeyFormCode,
    ResourceList,
} from './ActivePointModal';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';

interface Props {
    close: () => void;
    refresh: () => void;
}

type ActiveTanantRes = {
    result: boolean;
    message: {
        tenantLicense: ErrorInfos;
        tenantConfig: ErrorInfos;
    };
};

export default (props: Props) => {
    const { close, refresh } = props;
    const [loading, setLoading] = useState(false);

    const tenantUploadRef = useRef<UploadRef>();
    const resourceUploadRef = useRef<UploadRef>();

    const [form] = Form.useForm();

    async function handleOk() {
        if (
            tenantUploadRef.current?.hasUploading() ||
            resourceUploadRef.current?.hasUploading()
        ) {
            message.warn(
                i18n.t('@base:@message__common.uploading', '正在上传'),
            );
            return;
        }
        const values = await form.validateFields();
        const configLength = values.tanantConfigFileId?.length;
        const tanantConfigFileId =
            configLength > 0
                ? values.tanantConfigFileId[configLength - 1]
                : undefined;
        let params: any = {
            tenantLicense: values.tenantLicense?.trim(),
            tenantConfigFileId: tanantConfigFileId,
        };

        setLoading(true);
        try {
            const _data = (await request({
                method: 'post',
                url: '/base-server-service/api/v1/license/tenant/active/not/login',
                data: params,
            })) as unknown;
            const { result, messsage: messageInfo } = _data as ActiveTanantRes;
            if (result) {
                message.success(
                    i18n.t(
                        '@base:@action__login.active.tenant.success',
                        '激活租户成功',
                    ),
                );
                closeModal();
            } else {
                dealErrorTips(messageInfo);
            }
        } catch (e) {
            message.error(e);
        } finally {
            setLoading(false);
        }
    }

    /**
     * @description 处理错误提示，设置表单的错误信息
     * @param {ActiveTanantRes['message']} data
     */
    function dealErrorTips(data: ActiveTanantRes['message']) {
        const fieldsTips: { name: string; errors: string[] }[] = [];
        // 后端返回的字段和表单映射
        const mapTmp: Record<string, string> = {
            tenantLicense: 'tenantLicense',
            tenantConfig: 'tanantConfigFileId',
        };
        for (let [key, value] of Object.entries(data)) {
            if (key == 'tenantConfig') {
                const formatInfo = (value as ResourceList).map(
                    ({ fileId, errorCode }) => ({
                        fileId: fileId,
                        errprTips: getI18KeyFormCode(errorCode)?.key,
                    }),
                );
                tenantUploadRef.current?.dealFileListErrorTips(formatInfo);
            } else if (value?.errorCode) {
                fieldsTips.push({
                    name: mapTmp[key],
                    errors: [getI18KeyFormCode(value?.errorCode as number)],
                });
            }
        }
        form.setFields(fieldsTips);
    }

    function handleCancel() {
        if (loading) return;
        closeModal();
    }
    function closeModal() {
        refresh();
        close();
    }

    return (
        <Modal
            visible
            onCancel={handleCancel}
            width={420}
            title={tt('@base:@action__login.active.tenant.title', '激活租户')}
            onOk={handleOk}
            cancelButtonProps={{ disabled: loading }}
            okButtonProps={{ disabled: loading }}
            wrapClassName={loading ? 'modal-disabled-close-icon' : ''}
            okText={tt('@base:@action__login.active.modal.ok', '确定')}
            cancelText={tt('@base:@action__login.active.modal.cancel', '取消')}
            className="login-active-modal"
        >
            <Spin spinning={loading}>
                <Form form={form} layout="vertical">
                    <AFormItem
                        name="tenantLicense"
                        label={tt(
                            '@base:@action__login.active.tenant.label.tenantLicense',
                            '租户License',
                        )}
                        rules={[
                            {
                                required: true,
                                message: tt(
                                    '@base:@message__login.active.tenant.required.message.tenantLicense',
                                    '租户License不能为空',
                                ),
                            },
                        ]}
                    >
                        <Input
                            placeholder={i18n.t(
                                '@base:@action__login.active.tenant.placeholder.tenantLicense',
                                '请输入租户License',
                            )}
                        />
                    </AFormItem>
                    <AFormItem
                        label={tt(
                            '@base:@action__login.active.tenant.label.tanantConfigFileId',
                            '租户配置包',
                        )}
                        name="tanantConfigFileId"
                    >
                        <Upload
                            uploadProps={{ maxCount: 1 }}
                            ref={tenantUploadRef}
                            accept=".pem"
                        />
                    </AFormItem>
                </Form>
            </Spin>
        </Modal>
    );
};

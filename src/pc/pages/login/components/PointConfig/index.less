@import '../../../../style/common';
.point-config-container {
    position: absolute;

    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background-color: #ffffff;
    border-radius: 64px;

    &:hover {
        cursor: pointer;
        .icon {
            color: @main-color;
        }
    }

    .icon {
        font-size: 24px;
        // color: @main-color;
    }
    .active-point {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }
}

.modal-disabled-close-icon {
    .poppy-modal-close {
        cursor: not-allowed;
    }
}

.login-active-modal {
    .poppy-modal-content {
        .poppy-btn:not(.poppy-btn-primary):hover,
        .poppy-btn:not(.poppy-btn-primary):focus {
            color: @main-color-hover;
            border-color: @main-color-hover;
        }
        .poppy-btn:not(.poppy-btn-primary):active {
            color: @main-color-active;
            border-color: @main-color-active;
        }
    }
}

/*
 * @Author: yxfan
 * @Date: 2024-06-18 14:36:26
 * @LastEditTime: 2024-09-09 18:15:16
 * @Description: 激活局点
 */
import { useState, useRef } from 'react';
import { Modal, Spin, Form, Input, Button, message } from '@streamax/poppy';
import Upload, { UploadRef } from '@/pc/component/Upload';
import { tt } from '@/utils/i18n';
import request from '@/utils/request';
import i18n from 'i18next';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';

interface Props {
    close: () => void;
    refresh: () => void;
}

export type ResourceList = {
    fileId: string;
    errorCode: number;
}[];

export type ErrorInfos = {
    errorCode: number;
    errorMessage: string;
};

type ActivePointRes = {
    result: boolean;
    message: {
        pointLicense: ErrorInfos;
        pointConfig: ErrorInfos;
        resoureFileList: ResourceList;
    };
};

export function getI18KeyFormCode(code: number): string {
    // TODO
    const message = tt(`@base:@return__${code}`);

    if (message?.key == `@base:@return__${code}`) {
        return tt(`@base:@return__120020119`);
    }
    return message;
}

export default (props: Props) => {
    const { close, refresh } = props;
    const [loading, setLoading] = useState(false);

    const pointUploadRef = useRef<UploadRef>();
    const resourceUploadRef = useRef<UploadRef>();

    const [form] = Form.useForm();

    async function handleOk() {
        if (
            pointUploadRef.current?.hasUploading() ||
            resourceUploadRef.current?.hasUploading()
        ) {
            message.warn(
                i18n.t('@base:@message__common.uploading', '正在上传'),
            );
            return;
        }
        const values = await form.validateFields();
        const configLength = values.pointConfigFileId?.length;
        let params: any = {
            pointLicense: values.pointLicense?.trim(),
            resoureFileList: values?.resoureFileIdList || [],
            pointConfig:
                values.pointConfigFileId[configLength - 1] || undefined,
        };

        setLoading(true);
        try {
            const _data = (await request({
                method: 'post',
                url: '/base-server-service/api/v1/license/point/active/not/login',
                data: params,
            })) as unknown;
            const { result, messsage: messageInfo } = _data as ActivePointRes;
            if (result) {
                message.success(
                    i18n.t(
                        '@base:@action__login.active.point.success',
                        '激活局点成功',
                    ),
                );
                closeModal();
            } else {
                dealErrorTips(messageInfo);
            }
        } catch (e) {
            message.error(e);
        } finally {
            setLoading(false);
        }
    }

    /**
     * @description 处理错误提示，设置表单的错误信息
     * @param {ActivePointRes['message']} data
     */
    function dealErrorTips(data: ActivePointRes['message']) {
        const fieldsTips: { name: string; errors: string[] }[] = [];
        // 后端返回的字段和表单映射
        const mapTmp: Record<string, string> = {
            pointLicense: 'pointLicense',
            pointConfig: 'pointConfigFileId',
        };
        for (let [key, value] of Object.entries(data)) {
            if (key == 'resoureFileList') {
                const formatInfo = (value as ResourceList).map(
                    ({ fileId, errorCode }) => ({
                        fileId: fileId,
                        errprTips: getI18KeyFormCode(errorCode)?.key,
                    }),
                );
                resourceUploadRef.current?.dealFileListErrorTips(formatInfo);
            } else {
                if (value?.errorCode) {
                    fieldsTips.push({
                        name: mapTmp[key],
                        errors: [getI18KeyFormCode(value?.errorCode as number)],
                    });
                }
            }
        }
        form.setFields(fieldsTips);
    }

    function handleCancel() {
        if (loading) return;
        closeModal();
    }

    function closeModal() {
        refresh();
        close();
    }
    return (
        <Modal
            visible
            onCancel={handleCancel}
            width={420}
            maskClosable={false}
            title={tt('@base:@action__login.active.point.title', '激活局点')}
            onOk={handleOk}
            cancelButtonProps={{ disabled: loading }}
            okButtonProps={{ disabled: loading }}
            wrapClassName={loading ? 'modal-disabled-close-icon' : ''}
            okText={tt('@base:@action__login.active.modal.ok', '确定')}
            cancelText={tt('@base:@action__login.active.modal.cancel', '取消')}
            className="login-active-modal"
        >
            <Spin spinning={loading}>
                <Form form={form} layout="vertical">
                    <AFormItem
                        name="pointLicense"
                        label={tt(
                            '@base:@action__login.active.point.label.license',
                            '局点License',
                        )}
                        rules={[
                            {
                                required: true,
                                message: tt(
                                    '@base:@message__login.active.point.required.message.username',
                                    '局点License不能为空',
                                ),
                            },
                        ]}
                    >
                        <Input
                            placeholder={i18n.t(
                                '@base:@action__login.active.point.placeholder.license',
                                '请输入局点License',
                            )}
                        />
                    </AFormItem>
                    <AFormItem
                        label={tt(
                            '@base:@action__login.active.point.label.pointConfigFileId',
                            '局点配置包',
                        )}
                        name="pointConfigFileId"
                        rules={[
                            {
                                required: true,
                                message: tt(
                                    '@base:@message__login.required.message.pointConfigFileId',
                                    '请上传局点配置文件',
                                ),
                            },
                        ]}
                    >
                        <Upload
                            uploadProps={{ maxCount: 1 }}
                            ref={pointUploadRef}
                            accept=".zip"
                        />
                    </AFormItem>
                    <AFormItem
                        label={tt(
                            '@base:@action__login.active.point.label.resoureFileIdList',
                            '应用资源包',
                        )}
                        name="resoureFileIdList"
                        rules={[
                            {
                                required: true,
                                message: tt(
                                    '@base:@message__login.required.message.resoureFileIdList',
                                    '请上传应用资源包',
                                ),
                            },
                        ]}
                    >
                        <Upload
                            uploadProps={{ maxCount: 1 }} // 2.15.3 只支持单个资源包上传，放开限制注释掉该参数
                            ref={resourceUploadRef}
                            accept=".zip"
                        />
                    </AFormItem>
                </Form>
            </Spin>
        </Modal>
    );
};

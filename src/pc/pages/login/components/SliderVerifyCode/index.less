@import '../../../../style/common';
@import '../../../../../../node_modules/@streamax/poppy-themes/starry/abroad.less';

.login-slider-verify-code {
    width: 440px;
    .header {
        display: flex;
        align-items: center;
        .icon-back {
            z-index: 2;
            display: inline-block;
            padding: 2px 8px;
            color: @main-color;
            border: 1px solid @main-color;
            border-radius: 2px;
            cursor: pointer;
            .icon {
                position: relative;
                top: 2px;
            }
            &::abroad {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                width: 36px;
                width: 30px;
                height: 24px;
                height: 24px;
                margin-top: -8px;
                padding: unset;
                background-color: @starry-bg-color-container;
                border-radius: 4px;
            }
        }
        .title {
            width: 100%;
            margin-left: -32px;
            font-size: 28px;
            text-align: center;
            .login-page-title();
            .inner-title {
                display: inline-block;
                display: inline-block;
                width: 100%;
                margin-left: 32px;
                padding-right: 48px;
                padding-left: 48px;
                overflow: hidden;
                line-height: 54px;
                white-space: nowrap;
                text-overflow: ellipsis;
                transform: translateX(-32px);
            }
        }
    }
    .tool-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 36px;
        &::abroad {
            margin-top: 40px;
        }
        .message-text {
            &::abroad {
                color: rgba(0, 0, 0, 0.45);
                &.success {
                    color: #52c41a;
                }
                &.error {
                    color: #ff4d4f;
                }
                &.warn {
                    color: #faad14;
                }
            }
            &.success {
                color: #52c41a;
            }
            &.error {
                color: #ff4d4f;
            }
            &.warn {
                color: #faad14;
            }
        }
        .icon-refresh {
            cursor: pointer;
        }
    }

    .cover-message {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.85);
        &.success {
            color: #52c41a;
        }
        &.error {
            color: #ff4d4f;
        }
        &.warn {
            color: #faad14;
        }
        .cover-message-icon {
            font-size: 64px;
            line-height: 64px;
        }
        .cover-message-text {
            margin-top: 16px;
        }
    }

    .verify-code-wrapper {
        position: relative;
        height: 248px;
        margin-top: 16px;
        background: #f2f3f5;
        // 抖动
        @keyframes shaking {
            0% {
                transform: translate(0, 0);
            }
            25% {
                transform: translate(8px, 0);
            }
            50% {
                transform: translate(0, 0);
            }
            75% {
                transform: translate(-8px, 0);
            }
            100% {
                transform: translate(0, 0);
            }
        }
        &.shaking {
            animation: shaking 0.2s infinite;
        }
        .background-image {
            width: 100%;
            height: 248px;
            &::abroad {
                border-radius: 8px;
            }
        }
        .block-image {
            position: absolute;
            width: 40px;
            height: 40px;
            filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.3));
        }
        .icon-no-picture {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
    .slider-wrapper {
        top: 14px;
    }
}

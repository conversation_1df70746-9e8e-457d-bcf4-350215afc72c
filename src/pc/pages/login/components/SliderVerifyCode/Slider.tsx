import React, {
    useImperativeHandle,
    forwardRef,
    useRef,
    useEffect,
} from 'react';
import { useGetState } from '@streamax/hooks';
import { getScale } from '@/utils/common';
import { ReactComponent as IconVerticalbarLine } from '@/assets/icons/icon-verticalbar-line.svg';
import cn from 'classnames';
import './Slider.less';

interface SliderProps {
    disabled?: boolean; // 是否禁用
    onChange?: (value: number) => void; // 位置变化事件
    onFinish?: (value: number) => void; // 拖动完成后的回调
    onMouseDownCallBack?: () => void; // 滑块鼠标按下事件
}

export interface SliderRef {
    reset: () => void; // 重置
}

const Slider: React.ForwardRefRenderFunction<SliderRef, SliderProps> = (
    props,
    ref,
) => {
    const { disabled, onChange, onFinish, onMouseDownCallBack } = props;

    // 滑块距离左边的距离。拖动滑块到左边的进度值
    const [sliderBlockOffsetX, setSliderBlockOffsetX, getSliderBlockOffsetX] =
        useGetState<number>(0);
    // 开始拖动的位置
    const startPageX = useRef<number>(0);
    // 容器dom
    const sliderRef = useRef<HTMLDivElement>(null);
    // 滑块dom
    const sliderBlockRef = useRef<HTMLDivElement>(null);
    // 容器React对象
    const sliderRectRef = useRef<any>();
    // 滑块React对象
    const sliderBlockRectRef = useRef<any>();

    useImperativeHandle(ref, () => ({
        reset: () => {
            setSliderBlockOffsetX(0);
        },
    }));

    useEffect(() => {
        settingBoundingClientRect();
        window.addEventListener('resize', settingBoundingClientRect);
        return () => {
            window.removeEventListener('resize', settingBoundingClientRect);
        };
    }, []);

    useEffect(() => {
        onChange?.(sliderBlockOffsetX);
    }, [sliderBlockOffsetX]);

    function settingBoundingClientRect() {
        sliderRectRef.current = sliderRef.current?.getBoundingClientRect();
        sliderBlockRectRef.current =
            sliderBlockRef.current?.getBoundingClientRect();
    }

    const handleMouseMove = (e: any) => {
        // 缩放等级，由于登录界面存在缩放，因此通过pageX计算出来的值与界面显示的值会出现不匹配的问题
        // 这里需要通过缩放等级，对变化的像素值进行换算
        const scale = getScale();
        // 容器宽度
        const sliderWidth = sliderRectRef.current?.width || 0;
        const sliderBlockWidth = sliderBlockRectRef.current?.width || 0;
        // 拖动变化的距离
        const deffer = (e.pageX - startPageX.current) / scale;
        // 最终滑块的位置
        let nextSliderBlockOffsetX = sliderBlockOffsetX + deffer;
        const min = 0;
        const max = (sliderWidth - sliderBlockWidth) / scale;
        // 处理边界
        if (nextSliderBlockOffsetX < min) {
            nextSliderBlockOffsetX = min;
        } else if (nextSliderBlockOffsetX > max) {
            nextSliderBlockOffsetX = max;
        }
        setSliderBlockOffsetX(nextSliderBlockOffsetX);
    };

    function handleMouseUp() {
        // 计算百分比
        const sliderWidth = sliderRectRef.current.width / getScale();
        onFinish?.(Math.floor((getSliderBlockOffsetX() * 1000) / sliderWidth));
        delDocumentEventListener();
    }

    function addDocumentEventListener() {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    function delDocumentEventListener() {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    }

    // 点击滑块
    function handleOnMouseDown(e: any) {
        startPageX.current = e.pageX;
        if (disabled) return;
        addDocumentEventListener();
        e.preventDefault();
        onMouseDownCallBack?.();
    }

    function handleStopPropagation(e: any) {
        e.preventDefault();
    }

    return (
        <div
            className={cn('login-slider-verify-code-slider', {
                disabled: disabled,
            })}
            ref={sliderRef}
        >
            {/* 滑块划过的位置的背景 */}
            <div
                className="after-background"
                style={{
                    width: `${sliderBlockOffsetX}px`,
                }}
            />
            {/* 滑块 */}
            <div
                ref={sliderBlockRef}
                className="slider-block"
                onClick={handleStopPropagation}
                onMouseDown={handleOnMouseDown}
                style={{
                    position: 'absolute',
                    left: `${sliderBlockOffsetX}px`,
                }}
            >
                <IconVerticalbarLine />
            </div>
        </div>
    );
};

export default forwardRef(Slider);

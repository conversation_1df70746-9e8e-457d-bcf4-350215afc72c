import React, {
    useState,
    useEffect,
    useRef,
    useImperativeHandle,
    forwardRef,
} from 'react';
import Slider from './Slider';
import type { SliderRef } from './Slider';
import { Spin } from '@streamax/poppy';
import { ReactComponent as IconBack } from '@/assets/icons/icon-back02-line.svg';
import { ReactComponent as IconRefresh } from '@/assets/icons/icon_refresh.svg';
import { ReactComponent as IconNoPicture } from '@/assets/icons/icon-state-nopicture.svg';
import {
    IconSuccessFill,
    IconCloseCircleFill,
    IconInformationFill,
} from '@streamax/poppy-icons';
import cn from 'classnames';
import i18n from 'i18next';
import './index.less';

// 验证码信息
export type VerifyCodeInfo = {
    backgroundImage: string; // 背景图
    blockImage: string; // 拼块图
    offsetY: number; // 拼块距离顶部的位置（百分比）
    uuid: string; // 图片的UUID
};

// 提示信息位置
enum MessagePosition {
    'top',
    'cover',
}

// 提示信息
export type MessageInfo = {
    type: 'success' | 'error' | 'warn'; // 信息类型
    message: string; // 信息内容
    autoCloseTime?: number; // 自动关闭时间 为0时，不自动关闭
    position?: MessagePosition; // 提示信息展示位置【顶部，覆盖图片】
};

interface SliderVerifyCodeProps {
    verifyCodeInfo?: VerifyCodeInfo; // 验证码信息
    sliderDisabled?: boolean; // 滑块是否禁用
    messageAutoCloseTime?: number; // 提示信息关闭时间：在调用提示信息方法时，提示信息展示的时间【毫秒】
    shakeTime?: number; // 验证码框抖动时间：在调用抖动方法时，验证码框会差生抖动效果【毫秒】
    loading?: boolean; // loading效果
    onFinish?: (value: number) => void; // 完成拼图
    onRefresh?: () => void; // 刷新
    onClose?: () => void; // 关闭
    onSliderMouseDown?: () => void; // 滑块鼠标按下
}

export interface SliderVerifyCodeRef {
    reset: () => void; // 重置滑块
    message: (
        topMessageInfo: MessageInfo, // 信息内容
        callback?: () => void, // 回调函数。【例如：显示成功消息后，跳转到系统页】
    ) => void; // 展示提示信息
    shake: (time?: number) => void; // 抖动 可自定义抖动时间
}

// 提示信息默认关闭时间
const DEFAULT_MSG_CLOSE_TIME = 2000;
// 默认抖动时间
const DEFAULT_SHAKE_TIME = 300;

const MESSAGE_ICON_TYPE = {
    success: <IconSuccessFill />,
    error: <IconCloseCircleFill />,
    warn: <IconInformationFill />,
};

const SliderVerifyCode: React.ForwardRefRenderFunction<
    SliderVerifyCodeRef,
    SliderVerifyCodeProps
> = (props, ref) => {
    const {
        verifyCodeInfo,
        sliderDisabled = false,
        messageAutoCloseTime,
        loading = false,
        onClose,
        onFinish,
        onRefresh,
        onSliderMouseDown,
    } = props;

    // 滑块位置
    const [offsetX, setOffsetX] = useState<number>(0);
    // 提示信息
    const [topMessageInfo, setTopMessageInfo] = useState<MessageInfo | null>();
    const [coverMessageInfo, setCoverMessageInfo] =
        useState<MessageInfo | null>();
    // 是否抖动
    const [isShaking, setIsShaking] = useState<boolean>(false);
    // 滑动条
    const sliderRef = useRef<SliderRef>(null);

    // 自动关闭提示信息定时器
    const autoCloseMessageTimeout = useRef<NodeJS.Timeout>();
    // 验证码抖动定时器
    const shakingTimeout = useRef<NodeJS.Timeout>();

    useEffect(() => {
        if (topMessageInfo) handleMessage(topMessageInfo, MessagePosition.top);
    }, [topMessageInfo]);

    useEffect(() => {
        if (coverMessageInfo)
            handleMessage(coverMessageInfo, MessagePosition.cover);
    }, [coverMessageInfo]);

    const sliderReset = () => {
        setOffsetX(0);
        sliderRef.current?.reset();
    };

    useImperativeHandle(ref, () => ({
        reset: sliderReset,
        message: (msg) => {
            if (!msg) {
                setCoverMessageInfo(null);
                setTopMessageInfo(null);
                return;
            }
            const { position } = msg;
            position === MessagePosition.cover
                ? setCoverMessageInfo(msg)
                : setTopMessageInfo(msg);
        },
        shake: (time) => {
            setIsShaking(true);
            shakingTimeout.current && clearTimeout(shakingTimeout.current);
            shakingTimeout.current = setTimeout(() => {
                setIsShaking(false);
            }, time || DEFAULT_SHAKE_TIME);
        },
    }));

    // 处理提示信息
    function handleMessage(message: MessageInfo, type: MessagePosition) {
        // 清除定时器
        autoCloseMessageTimeout.current &&
            clearTimeout(autoCloseMessageTimeout.current);
        const { autoCloseTime } = message;
        let closeTime = DEFAULT_MSG_CLOSE_TIME;
        // 默认关闭时间
        if (messageAutoCloseTime !== undefined)
            closeTime = messageAutoCloseTime;
        // 调用是指的关闭时间
        if (autoCloseTime !== undefined) closeTime = autoCloseTime;
        // 存在关闭时间
        if (closeTime !== 0) {
            autoCloseMessageTimeout.current = setTimeout(() => {
                type === MessagePosition.cover
                    ? setCoverMessageInfo(null)
                    : setTopMessageInfo(null);
            }, closeTime);
        }
    }

    function handleClose() {
        onClose?.();
    }

    // 完成滑动
    function handleOnFinish(value: number) {
        // [67508]解决网络慢时滑块信息接口还在请求中，已经拖拽完成时，会向后端发起请求，接口报参数错误的问题。
        if (!verifyCodeInfo) {
            sliderReset();
            return;
        }
        onFinish?.(value);
    }
    /**
     * 滑块鼠标按下事件
     */
    function handleOnSliderMouseDown() {
        onSliderMouseDown?.();
    }

    return (
        <div className="login-slider-verify-code">
            {/* 头部信息 */}
            <div className="header">
                <div className="icon-back" onClick={handleClose}>
                    <a className="icon">
                        <IconBack />
                    </a>
                </div>
                <span className="title">
                    <span
                        className="inner-title"
                        title={i18n.t(
                            '@base:@message__login.security.verification',
                        )}
                    >
                        {i18n.t('@base:@message__login.security.verification')}
                    </span>
                </span>
            </div>
            {/* 工具栏 */}
            <div className="tool-bar">
                <span className={cn('message-text', topMessageInfo?.type)}>
                    {topMessageInfo?.message ||
                        i18n.t(
                            '@base:@message__login.verification.drag.message',
                        )}
                </span>
                <span className="icon-refresh" onClick={() => onRefresh?.()}>
                    <IconRefresh />
                </span>
            </div>
            {/* 内容区 */}
            <div
                className={cn('verify-code-wrapper', {
                    shaking: isShaking,
                })}
            >
                <Spin spinning={loading}>
                    {verifyCodeInfo ? (
                        <>
                            {/* 背景图 */}
                            <img
                                className="background-image"
                                src={`data:image/jpg;base64,${verifyCodeInfo?.backgroundImage}`}
                            />
                            {/* 拼块图 */}
                            <img
                                className="block-image"
                                src={`data:image/jpg;base64,${verifyCodeInfo?.blockImage}`}
                                style={{
                                    top: `${verifyCodeInfo?.offsetY}%`,
                                    left: offsetX,
                                }}
                            />
                        </>
                    ) : (
                        <div className="background-image">
                            <div className="icon-no-picture">
                                {/* 默认背景图 */}
                                <IconNoPicture />
                            </div>
                        </div>
                    )}
                    {coverMessageInfo && (
                        <div
                            className={cn(
                                'cover-message',
                                coverMessageInfo.type,
                            )}
                        >
                            <div className="cover-message-icon">
                                {MESSAGE_ICON_TYPE[coverMessageInfo.type]}
                            </div>
                            <div className="cover-message-text">
                                {coverMessageInfo.message}
                            </div>
                        </div>
                    )}
                </Spin>
            </div>
            {/* 底部滑动条 */}
            <div className="slider-wrapper">
                <Slider
                    ref={sliderRef}
                    disabled={sliderDisabled}
                    onChange={(value) => setOffsetX(value)}
                    onFinish={handleOnFinish}
                    onMouseDownCallBack={handleOnSliderMouseDown}
                />
            </div>
        </div>
    );
};

export default forwardRef(SliderVerifyCode);

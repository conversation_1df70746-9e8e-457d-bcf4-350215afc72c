import { Tooltip } from '@streamax/poppy';
import { useState, useEffect, useMemo } from 'react';
import { isRightLoginStyle } from '@/pc/component/LoginFormBg';
import { IconUnionFill } from '@streamax/poppy-icons';
import request from '@/utils/request';

import './index.less';
import { tt } from '@/utils/i18n';

/** @Description UI设计稿上的边距 */
const DISTANCE_DESIGN = 64;

interface Props {
    scale: number;
    config?: {
        loginPageStyle: number;
    };
}
export default (props: Props) => {
    const { scale, config } = props;
    const [interfaceButtonVis, setInterfaceButtonVis] = useState(false);
    // 接口中心，接口文档的key
    const INTERFACE_CONFIG_KEYS =
        'help.center.config.interfaceOverview,help.center.config.apiDocument';

    useEffect(() => {
        handleNotLoginConfig();
    }, []);

    const handleNotLoginConfig = async () => {
        try {
            const data = await request.get(
                '/base-server-service/api/v1/application/config/notlogin',
                {
                    params: {
                        keys: INTERFACE_CONFIG_KEYS,
                    },
                },
            );
            if (data && data?.length) {
                const show = data.filter((item: any) => {
                    return getLoginView(item.value);
                });
                if (show?.length) {
                    setInterfaceButtonVis(true);
                }
            }
            return data;
        } catch (e) {
            return Promise.reject();
        }
    };

    const getLoginView = (JSONString: string) => {
        try {
            const parsedObject = JSON.parse(JSONString);
            return parsedObject?.loginView ? true : false;
          } catch (error) {
            return false;
          }
    }

    const style = useMemo(() => {
        const distance = DISTANCE_DESIGN * scale;
        if (config) {
            if (isRightLoginStyle(config?.loginPageStyle)) {
                return { top: distance, left: distance };
            }
            return { top: distance, right: distance };
        } else {
            return {};
        }
    }, [scale, config?.loginPageStyle]);

    const renderInterfaceCenter = () => {
        return (
            <Tooltip
                title={tt(
                    '@base:@action_login.interface.center.title',
                    '接口中心',
                )}
                placement={
                    isRightLoginStyle(config?.loginPageStyle) ? 'right' : 'left'
                }
            >
                <div
                    className="active-btn"
                    onClick={() => {
                        const lang = window.localStorage.getItem('LOGIN_LANG');
                        window.open(
                            `${location.origin}/base/docs/notlogin/interface-center?lang=${lang}`,
                            'target',
                        );
                    }}
                >
                    <IconUnionFill className="icon" />
                </div>
            </Tooltip>
        );
    };

    return (
        <>
            {interfaceButtonVis && (
                <div
                    className="btn-config-container"
                    style={{ ...style, transform: `scale(${scale})` }}
                >
                    {renderInterfaceCenter()}
                </div>
            )}
        </>
    );
};

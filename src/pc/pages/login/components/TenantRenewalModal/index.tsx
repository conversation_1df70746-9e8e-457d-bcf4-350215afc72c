/*
 * @LastEditTime: 2024-09-26 09:07:24
 */
import { tt } from '@/utils/i18n';
import { Form, Input, message, Modal } from '@streamax/poppy';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';
import Upload, { UploadRef } from '@/pc/component/Upload';
import { useEffect, useRef, useState } from 'react';
import request from '@/utils/request';
import { ErrorInfos } from '../PointConfig/ActivePointModal';
type TenantRenewalModalProps = {
    visible: boolean;
    tenantInfo: Record<string, string>;
    onCancel: () => void;
    onOk: () => void;
};
type ActiveTanantRes = {
    result: boolean;
    message: {
        tenantLicense: ErrorInfos;
        tenantConfig: ErrorInfos;
    };
};
const baseUrl = window.APP_CONFIG['gateway.public.url'];
const TenantRenewalModal = (props: TenantRenewalModalProps) => {
    const { visible, tenantInfo, onCancel, onOk } = props;
    const [modalForm] = Form.useForm();
    const [submitLoading, setSubmitLoading] = useState(false);
    const tenantUploadRef = useRef<UploadRef>();
    useEffect(() => {
        modalForm.setFieldsValue({
            tenantName: tenantInfo?.tenantName,
            tenantId: tenantInfo?.tenantCode,
        });
    }, [tenantInfo]);
    useEffect(() => {
        if (!visible) {
            tenantUploadRef.current?.setFileList([]);
            modalForm.setFieldsValue({
                tenantConfigFileId: null,
            });
        }
    }, [visible]);

    const submit = async () => {
        const values = await modalForm.validateFields();
        setSubmitLoading(true);
        try {
            const params: any = {
                tenantId: tenantInfo.tenantId,
                tenantConfigFileId: values.tenantConfigFileId,
                operatorId: tenantInfo.userId,
            };
            const _data = (await request({
                method: 'post',
                url: '/base-server-service/api/v1/license/tenant/active/not/login',
                data: params,
            })) as unknown;
            const { result, messsage } = _data as ActiveTanantRes;
            if (result) {
                message.success(
                    tt(
                        '@base:@message__tenant.renewal.modal.renewal.success',
                        '租户续期成功',
                    ),
                );
                onOk?.();
            } else {
                const errorItem = messsage.tenantConfig?.[0];
                message.error(
                    tt(`@base:@return__${errorItem?.errorCode}`) ||
                        errorItem.errorMessage,
                );
            }
        } catch (e) {
            message.error(e);
        } finally {
            setSubmitLoading(false);
        }
    };
    return (
        <Modal
            visible={visible}
            className="license-renew-open-modal"
            title={tt('@base:@message__tenant.renewal.modal.title', '租户续期')}
            onCancel={() => {
                if (submitLoading) return;
                onCancel?.();
            }}
            onOk={submit}
            cancelButtonProps={{ disabled: submitLoading }}
            okButtonProps={{ disabled: submitLoading }}
            wrapClassName={submitLoading ? 'modal-disabled-close-icon' : ''}
            okText={tt('@base:@action__login.active.modal.ok', '确定')}
            cancelText={tt('@base:@action__login.active.modal.cancel', '取消')}
            width={400}
            confirmLoading={submitLoading}
        >
            <Form layout="vertical" form={modalForm} preserve={false}>
                <AFormItem
                    name="tenantName"
                    label={tt(
                        '@base:@message__tenant.renewal.modal.tenant.name',
                        '租户名称',
                    )}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input disabled={true} />
                </AFormItem>
                <AFormItem
                    name="tenantId"
                    label={tt(
                        '@base:@message__tenant.renewal.modal.tenant.id',
                        '租户ID',
                    )}
                    required
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input disabled={true} />
                </AFormItem>
                <AFormItem
                    name="tenantConfigFileId"
                    label={tt(
                        '@base:@action__login.active.tenant.label.tanantConfigFileId',
                        '租户配置包',
                    )}
                    required
                    rules={[
                        {
                            required: true,
                            message: tt(
                                '@base:@message__login.required.message.tanantConfigFileId',
                                '请上传租户配置包',
                            ),
                        },
                    ]}
                >
                    <Upload
                        uploadProps={{
                            maxCount: 1,
                            action: `${baseUrl}/base-server-service/api/v1/license/point/file/upload`,
                            disabled: submitLoading,
                        }}
                        ref={tenantUploadRef}
                        accept=".pem"
                    />
                </AFormItem>
            </Form>
        </Modal>
    );
};
export default TenantRenewalModal;

import type { ReactElement } from 'react';
import { useEffect, useRef, useState } from 'react';
import { noContainerTt, tt } from '@/utils/i18n';
import { Image } from '@streamax/poppy';
import { useAsyncEffect, useDebounceEffect, useSize } from '@streamax/hooks';
import {
    LOGIN_STYLE5,
    LOGINSTYLE1,
    LOGINSTYLE2,
    LOGINSTYLE4,
} from '@/utils/constant';
import './index.less';
import { loginTitleStyleEnum } from '@/type';

export interface LogoWithTitleProps {
    onHeightChange?: (height?: number) => void;
    data: {
        loginPageStyle: number;
        loginTitleStyle: loginTitleStyleEnum;
        loginTitlePic?: {
            imageId: string;
            imageUrl: string;
            imageName?: string;
            imageType?: 'png' | 'jpg';
        };
    };
}

const cacheLogoInfo: Record<string, string> = {};

const LogoWithTitle = (props: LogoWithTitleProps) => {
    const { data, onHeightChange } = props || {};
    const { loginTitleStyle, loginTitlePic, loginPageStyle } = data || {};
    const failImgSrc = [
        LOGINSTYLE1,
        LOGINSTYLE2,
        LOGINSTYLE4,
        LOGIN_STYLE5,
    ].includes(loginPageStyle)
        ? require('../../../../../assets/fail_ee.svg')
        : require('../../../../../assets/fail_f2.svg');
    const [imgSrc, setImgSrc] = useState<string>('');

    const logoTextContainerRef = useRef<HTMLDivElement>(null);
    const size = useSize(logoTextContainerRef);

    useEffect(() => {
        // margin的高度
        const marginHeight = 64;
        // 以前title文字占比高度，为了不去全局改动高度，直接内部计算
        onHeightChange?.((size?.height || 0) + marginHeight);
    }, [size?.height]);

    useAsyncEffect(async () => {
        if (
            loginTitleStyle !== loginTitleStyleEnum.TEXT &&
            loginTitlePic?.imageId
        ) {
            let fileUrl = failImgSrc;
            if (cacheLogoInfo[loginTitlePic.imageId]) {
                fileUrl = cacheLogoInfo[loginTitlePic.imageId];
            } else {
                fileUrl = loginTitlePic.imageUrl;
                cacheLogoInfo[loginTitlePic.imageId] = loginTitlePic.imageUrl;
            }
            setImgSrc(fileUrl);
        }
    }, [loginTitlePic?.imageId]);

    const text = (
        <div
            className="text"
            title={noContainerTt('@base:@action__login.title')}
        >
            {noContainerTt('@base:@action__login.title')}
        </div>
    );
    const logo = (
        <div className="logo">
            {imgSrc ? (
                <Image
                    wrapperClassName="logo-image-wrap"
                    preview={false}
                    src={imgSrc}
                    fallback={failImgSrc}
                />
            ) : null}
        </div>
    );
    const mix = (
        <div className="mix">
            {logo}
            {text}
        </div>
    );

    const content: Record<loginTitleStyleEnum, ReactElement> = {
        [loginTitleStyleEnum.TEXT]: text,
        [loginTitleStyleEnum.LOGO]: logo,
        [loginTitleStyleEnum.MIX]: mix,
    };

    return (
        <div className="logo-text-container" ref={logoTextContainerRef}>
            {content[loginTitleStyle]}
        </div>
    );
};

export default LogoWithTitle;

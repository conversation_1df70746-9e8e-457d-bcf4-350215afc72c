// @ts-nocheck
import { useState, useEffect, useRef, useMemo } from 'react';
import { history, useLocation } from 'umi';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import {
    Button,
    Form,
    Input,
    message,
    Select,
    Checkbox,
    Modal,
    Tooltip,
    Image,
} from '@streamax/poppy';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { useLockFn, useAsyncEffect, useUpdate, useGetState } from '@streamax/hooks';
import qs from 'querystring';
// @ts-ignore
import md5 from 'js-md5';
import {
    encryptors,
    getScale,
    getRuntimeLocales,
    queryLoginConfig,
    getOffsetInfo,
    getDefaultLang,
} from '@/utils/common';
import classNames from 'classnames';
import { ReactComponent as UserIcon } from '@/assets/icons/icon_user.svg';
import { IconMicrosoftLogo, IconMobilePhone } from '@streamax/poppy-icons';
import AnimationBg from '@/pc/component/AnimationBg';
import SliderVerifyCode from './components/SliderVerifyCode';
import type {
    VerifyCodeInfo,
    SliderVerifyCodeRef,
} from './components/SliderVerifyCode';
import {
    USERAGREEMENTVERSION,
    PRIVACYPOLICYVERSION,
    getOs,
    generateUUID,
} from '@/utils/common';
import { DEFAULTRETRIEVEPASSWORD } from '@/utils/constant';
import {
    LOGINSTYLE1,
    LOGIN_STYLE7,
    LOGINSTYLE4,
    BG_CONTAINER_HEIGHT,
    BG_WITH_LOGO_CONTAINER_HEIGHT,
} from '../../../utils/constant';
import './index.less';

import { LegalType } from '../legalAgreement';
import LoginFormBg, {
    isRightLoginStyle,
    calcContainerHight,
} from '@/pc/component/LoginFormBg';
import { CUSTOM_AGREEMENT_TYPE } from '@/utils/constant';
import BlurImg from '@/pc/component/BlurImg';
import PointConfig from './components/PointConfig';
import InterfaceCenter from './components/InterfaceCenter';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';
import useSystemComponentStyle from '@/pc/themeStyle/useSystemComponentStyle';
import LogoWithTitle from './components/LogoWithTitle';
import { loginTitleStyleEnum } from '@/type';
import { useDebounceFn } from '@streamax/hooks';
import TenantRenewalModal from './components/TenantRenewalModal';
import { bindThirdAccount } from '@/service/login';

export const agreementMap = {
    agree: 'userAgreementCustomConfig',
    secret: 'privacyPolicyCustomConfig',
};

// RSA 2048 加密
const defaultLocales = require('../../../app.locale.json');
const encryptor = encryptors();
const langue = {
    ar_EG: 'العربية',
    bg_BG: 'български',
    ca_ES: 'Català',
    cs_CZ: 'Česky',
    de_DE: 'Deutsch',
    el_GR: 'Ελληνικά',
    en_GB: 'English',
    en_US: 'English',
    es_ES: 'Español',
    et_EE: 'Eestlane',
    fa_IR: 'فارسی',
    fi_FI: 'suomalainen',
    fr_BE: 'Français (Belgique)',
    fr_FR: 'Français',
    he_IL: 'עברית',
    hi_IN: 'हिन्दी',
    hr_HR: 'hrvatski',
    hu_HU: 'magyar',
    is_IS: 'Íslensku',
    id_ID: 'Orang indonesia',
    it_IT: 'Italiano',
    ja_JP: '日本語',
    kn_IN: 'ಕನ್ನಡ',
    ko_KR: '한국어',
    nb_NO: 'norsk språk',
    ne_NP: 'नेपाली',
    nl_BE: 'Nederlands (België)',
    nl_NL: 'Nederlands',
    pl_PL: 'Polski',
    pt_BR: 'Português (Brasil)',
    pt_PT: 'Português',
    sk_SK: 'slovenského jazyk',
    sr_RS: 'Српски',
    sl_SI: 'Slovensko',
    sv_SE: 'svenska',
    ta_IN: 'தமிழ் மொழி',
    th_TH: 'ไทย',
    tr_TR: 'Türk dili',
    ro_RO: 'românesc',
    ru_RU: 'русский',
    uk_UA: 'Українська',
    vi_VN: 'Tiếng Việt Nam',
    zh_CN: '简体中文',
    zh_TW: '繁體中文(台湾)',
    zh_HK: '繁體中文(香港)',
    tg_TJ: 'тоҷикӣ',
};
// 词条缓存
const FormItem = Form.Item;
const { Option } = Select;

// 滑块验证码最大使用次数
const SLIDER_CODE_USE_MAX_COUNT = 5;
// 轮询验证码时间（4分钟）
const SLIDER_CODE_INTERVAL_TIME = 4 * 60 * 1000;

// 组件是否已卸载的标志位（定义在组件外部）
let isComponentUnmounted = false;

// 登陆页样式三
const LOGINPAGESTYLE3 = 3;

const countDownTotal = 121;
let loginData = {};
let countDownTimer = null;
// 获取设备类型
const os = getOs();
// 只有PC才使用滑块验证
const h5ValidateCode = !os.isPc || os.isTablet;
export default () => {
    const location = useLocation();
    const [scale, setScale] = useState(getScale());

    const [form] = Form.useForm();

    const [errInfo, setErrInfo] = useState();

    const [languageTypes, setLanguageTypes] = useState<any>([]);

    const [tenantInfo, setTenantInfo] = useState({});
    const [areaCodes, setAreaCodes] = useState<any[]>([]);
    const [config, setConfig] = useState<any>(null);
    const [loginType, setLoginType] = useState(0);
    const [countdown, setCountDown] = useState(countDownTotal);
    const [bgImageUrl, setBgImageUrl] = useState<string>('');
    const [bgImageHash, setBgImageHash] = useState<string>(undefined);
    const [illustrationUrl, setIllustrationUrl] = useState<string>(''); // 当前使用的插图url
    const [illustrationHash, setIllustrationHash] = useState<string>('');
    const [illustrationStyle, setIllustrationStyle] = useState<boolean>(false); // 是否使用插图模式
    const [agreedCheck, setAgreedCheck] = useState<boolean>(true);
    const [modalVisible, setModalVisible] = useState<boolean>(false);
    const [i18nLoaded, setI18nLoaded] = useState<boolean>(false);
    // 控制隐私协议展示、隐藏
    const [protocolInfo, setProtocolInfo] = useState({
        isShowProtocol: true,
        isCheckConfirm: true,
    });
    const [codeSrc, setCodeSrc] = useState('');
    const [authParams, setAuthParams] = useState({
        uuid: '',
    });
    const [logoTitleHeight, setLogoTitleHeight] = useState<number>(0);
    const loginRef = useRef(null);
    const languageSelectRef = useRef(null);
    const areaCodeSelectRef = useRef(null);
    const codeRef = useRef(null);
    const asyncOperationRef = useRef<(() => Promise<void>)[]>([]);
    // 验证码框展示状态
    const [sliderVerifyCodeVisible, setSliderVerifyCodeVisible] =
        useState<boolean>(false);
    // 验证码信息
    const [sliderVerifyCodeInfo, setSliderVerifyCodeInfo] =
        useState<VerifyCodeInfo | null>(null);
    // 验证码是否禁止滑动
    const [sliderDisable, setSliderDisable] = useState<boolean>(true);
    // 验证码使用次数次数（同一张验证码）
    const [codeUsedCount, setCodeUsedCount, getCodeUsedCount] = useGetState<number>(0);
    // 图片验证码请求loading
    const [sliderVerifyCodeLoading, setSliderVerifyCodeLoading] =
        useState<boolean>(false);
    // 验证码
    const sliderVerifyRef = useRef<SliderVerifyCodeRef>(null);
    // 轮询请求验证码
    const sliderVerifyInterval = useRef();
    const FULL_SCREEN_CENTER_STYLES = [LOGINSTYLE4, LOGIN_STYLE7];

    const documentVisibleTime = useRef(Date.now());

    const countdingDown = countdown < countDownTotal;

    const isDefaultLoginType = loginType === 0;

    const forceUpdate = useUpdate();
    const { isAbroadStyle } = useSystemComponentStyle();

    const isCheckProtocol = protocolInfo.isCheckConfirm
        ? protocolInfo.isCheckConfirm && !protocolInfo.isShowProtocol
        : true;

    const getAreaCodes = async () => {
        try {
            const data = await request.get(
                '/base-server-service/api/v1/areacode/query',
            );
            setAreaCodes(data);
        } catch (e) {
            // message.error(e);
        }
    };
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        try {
            const data = await queryLoginConfig();
            // 处理隐私协议展示隐藏
            const privacyAgreementFlag = data.privacyAgreementFlag;
            // 只有设置了privacyAgreementFlag值为0，才隐藏不校验隐私协议，
            // 没有相关字段，或者为1，都需要校验
            setProtocolInfo({
                isShowProtocol: privacyAgreementFlag === 0 ? false : true,
                isCheckConfirm: data?.agreementCheckFlag === 0 ? false : true,
            });

            dataConfig = {
                ...data,
                privacyPolicyVersion:
                    data.privacyPolicyVersion || PRIVACYPOLICYVERSION,
                userAgreementVersion:
                    data.userAgreementVersion || USERAGREEMENTVERSION,
                retrievePassword:
                    data.retrievePassword || DEFAULTRETRIEVEPASSWORD,
                backGroundLoaded: true,
            };
            if (data?.defaultLoginType) {
                setLoginType(data?.defaultLoginType);
            }
            if (data?.languageList) {
                // 先将语言按照sort字段进行排序
                data.languageList = data.languageList
                    .sort((a: any, b: any) => {
                        return a.sort - b.sort;
                    })
                    .filter((i) => i.checked);
                setLanguageTypes(
                    data.languageList.map(
                        (item: { laguageName: string; sort: number }) => ({
                            label: langue[item.languageName],
                            value: item.languageName,
                        }),
                    ),
                );
            }
            if (data?.curUseImageId) {
                const hash = (data.images || []).find(
                    (item) => item.imageId == data?.curUseImageId,
                )?.hash;
                setBgImageHash(hash);
                setBgImageUrl(data?.curUseImageUrl);
            }
            if (data?.curUseIllustrationId) {
                const hash = (data.illustration || []).find(
                    (item) => item.imageId == data?.curUseIllustrationId,
                )?.hash;
                setIllustrationHash(hash);
                setIllustrationUrl(data?.curUseIllustrationUrl);
            }
            setIllustrationStyle(data?.loginIllustration || false);
        } catch (e) {
            // return null;
        }
        setConfig(dataConfig);
    };
    const addClickTip = (e) => {
        if (e.target.tagName === 'INPUT') {
            return;
        }
        setAgreedCheck(true);
    };
    const addDocumentClickListener = () => {
        document.addEventListener('click', addClickTip, true);
    };
    const setDocumentTitle = () => {
        document.title = i18n.t('@base:@action__login.title', '');
    };

    const removeQueryParam = (key: string | string[]) => {
        const url = new URL(window.location.href);
        if (typeof key === 'string') {
            url.searchParams.delete(key);
        } else {
            key.forEach((i) => {
                url.searchParams.delete(i);
            });
        }

        window.history.replaceState(null, '', url.toString()); // 更新地址栏但不刷新页面
    };
    //TODO: 去掉url中的bind_result_code，rebind_result_code参数，该参数是后端重定向指定的，以免重新登陆时再次提示错误
    //TODO: 该方法后续需要优化
    const removeParamFromRedirectUrl = (
        redirectUrl: string,
        paramName: string[],
    ) => {
        // 使用 URL 对象解析 URI
        const url = new URL(redirectUrl);
        paramName.forEach((i) => {
            if (url.searchParams.has(i)) {
                url.searchParams.delete(i);
            }
        });

        const locationUrl = new URL(window.location.href);
        const loginUrl = locationUrl.origin + '/login/pc/login';

        // 获取 locationUrl 的 query 参数，并拼接成字符串
        let queryParams = '';
        const searchParams = new URLSearchParams(locationUrl.search);

        // 遍历所有 query 参数，并替换 redirect_url
        searchParams.forEach((value, key) => {
            if (key === 'redirect_url') {
                // 替换为新的 redirect_url
                queryParams += `redirect_url=${window.encodeURIComponent(
                    url.href,
                )}`;
            } else {
                // 其他参数正常拼接
                queryParams += `${key}=${value}`;
            }
            // 每个参数之间用 & 连接
            queryParams += '&';
        });

        // 如果存在 redirect_url，已经替换，否则增加新的 redirect_url
        if (!searchParams.has('redirect_url') && redirectUrl) {
            queryParams += `redirect_url=${window.encodeURIComponent(
                url.href,
            )}`;
        }

        // 去除最后一个多余的 &
        if (queryParams.endsWith('&')) {
            queryParams = queryParams.slice(0, -1);
        }

        // 如果存在 query 参数，先拼接 '?'，否则直接拼接 loginUrl
        const callbackUrl = queryParams
            ? `${loginUrl}?${queryParams}`
            : `${loginUrl}`;
        window.history.replaceState(null, '', callbackUrl); // 更新地址栏但不刷新页面
    };

    useEffect(() => {
        if (!i18nLoaded) return;
        const query = location.query;
        const loginFailedCode = query.login_failed_code as string;
        const redirectUrl = query.redirect_url as string;
        const langKey =
            (query.langKey as string) || '@base:@return__' + loginFailedCode;
        const msg = query.message as string;
        if (loginFailedCode) {
            message.error(i18n.t(langKey, msg));
            removeQueryParam(['login_failed_code', 'langKey', 'message']);
        }
        if (redirectUrl) {
            removeParamFromRedirectUrl(redirectUrl, [
                'bind_result_code',
                'rebind_result_code',
            ]);
        }
    }, [location.search, i18nLoaded]);

    useEffect(() => {
        // 组件挂载时重置标志位
        isComponentUnmounted = false;
        window.addEventListener('resize', () => {
            setScale(getScale());
        });
        getLoginConfig();
        addDocumentClickListener();
        const handleVisibilityChange = () => {
            let timeNow = Date.now();
            // 窗口可见时，当可见与上次不可见的时间差大于4分钟时，重新获取验证码
            console.log('===[[[document.visibilityState]]]===', document.visibilityState, (timeNow - documentVisibleTime.current >= SLIDER_CODE_INTERVAL_TIME));
            if (document.visibilityState === 'visible' && (timeNow - documentVisibleTime.current >= SLIDER_CODE_INTERVAL_TIME)) {
                // 窗口重新变为可见时，重新获取验证码
                setupInterval();
            }
        };
        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            document.removeEventListener('click', addClickTip, true);
            document.removeEventListener(
                'visibilitychange',
                handleVisibilityChange,
            );
            // 组件卸载时设置标志位
            isComponentUnmounted = true;
            clearSliderVerifyTimer();
        };
    }, []);
    const runNextTask = async () => {
        asyncOperationRef.current.shift();
        if (asyncOperationRef.current.length > 0) {
            await asyncOperationRef.current[
                asyncOperationRef.current.length - 1
            ]();
        }
    };
    // const onceRef = useRef(false);
    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType || (!tenantId && tenantId != 0)) {
            runNextTask();
            return;
        }
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        forceUpdate();
        setDocumentTitle();
        runNextTask();
        setI18nLoaded(true);
    };

    useAsyncEffect(async () => {
        const query = history.location.query || {};
        const lang = query.lang as string;
        removeQueryParam('lang');
        const lng = getDefaultLang({
            defaultLang: config?.defaultLanguage,
            authLanguageList: config?.languageList.sort((a: any, b: any) => {
                    return a.sort - b.sort;
                }).filter((i) => i.checked).map(i=>i.languageName),
            languageInUrl: lang
        });
        form.setFieldsValue({
            langue: lng,
        });
        window.localStorage.setItem('LOGIN_LANG', lng);
        const asyncOperation = async () => {
            await i18nInit(lng, config?.tenantId);
        };
        asyncOperationRef.current.push(asyncOperation);
        if (asyncOperationRef.current.length === 1) {
            await asyncOperationRef.current[0]();
        }
    }, [config]);

    const onValuesChange = async (values: any) => {
        if (values.langue) {
            await i18nInit(values.langue, config?.tenantId);
            setDocumentTitle();
        }
    };
    const getSrc = () => {
        return new Promise((resolve) => {
            request
                .get('/base-server-service/api/v1/authcode/newObtain')
                .then((data: any) => {
                    resolve(data);
                });
        });
    };
    const refreshCode = async () => {
        const res: any = await getSrc();
        setAuthParams({
            uuid: res.uuid,
        });
        setCodeSrc(`data:image/gif;base64,${res.authCodePic}`);
    };
    const { run: _defanceGetSliderVerifyCodeInfo } = useDebounceFn(
        getSliderVerifyCodeInfo,
        { wait: 300 },
    );

    // 创建一个函数来设置定时器，这样可以在需要时重新设置
    const setupInterval = (isFetchFirst: boolean = true) => {
        // 如果组件已经卸载，则不再设置定时器
        if (isComponentUnmounted) {
            return;
        }

        // 清除之前的定时器（如果存在）
        clearSliderVerifyTimer();

        // 先获取一次新的验证码
        if (isFetchFirst) {
            _defanceGetSliderVerifyCodeInfo();
        }
        // 设置新的定时器
        sliderVerifyInterval.current = setInterval(() => {
            // 如果组件已经卸载，则清除定时器
            if (isComponentUnmounted) {
                clearSliderVerifyTimer();
                return;
            }
            _defanceGetSliderVerifyCodeInfo();
        }, SLIDER_CODE_INTERVAL_TIME);
    };

    const clearSliderVerifyTimer = () => {
        if (sliderVerifyInterval.current) {
            clearInterval(sliderVerifyInterval.current);
        }
    };

    useEffect(() => {
        if (!isDefaultLoginType && !areaCodes.length) {
            getAreaCodes();
        }
        // 平板登录时，填验证码
        if (
            isDefaultLoginType &&
            h5ValidateCode &&
            config?.verificationCodeSwitch === true
        ) {
            refreshCode();
        }
    }, [loginType, sliderVerifyCodeVisible, config]);

    useEffect(() => {
        // 清除定时器
        clearSliderVerifyTimer();
        // 账号密码登录且不是h5验证码的情况下，都需要轮询验证码（无论是否打开验证码）
        if (isDefaultLoginType && !h5ValidateCode) {
            // 初始设置定时器
            setupInterval();
        }
    }, [loginType, config]);


    // 计算登录框的左侧的位置只能左侧移动定位，右侧定位不生效
    const calculateLeftLocationDebounce = useMemo(() => {
        // 判断是否右侧布局，不是就返回0
        if (!isRightLoginStyle(config?.loginPageStyle)) return 0;
        const { width } = getOffsetInfo(document.body);
        const leftMax = width - 800 * scale > 0 ? width - 800 * scale : 0;
        return leftMax;
    }, [scale, config?.loginPageStyle]);

    function getRedirectUrl(url) {
        const whiteList = ['localhost', '127.0.0.1'];
        let flag = false;
        whiteList.forEach((item) => {
            url.indexOf(item) > -1 && (flag = true);
        });
        if (flag) {
            return url;
        }
        if (url !== window.location.hostname) {
            return '/';
        }
        return url;
    }

    // 修改密码
    const updatePassword = () => {
        history.push({
            pathname: '/login/pc/updatePassword',
            query: loginData,
        });
    };
    // 忘记密码
    const findPassword = () => {
        const query = history.location.query || {};
        const redirect_url = query.redirect_url as string;
        history.push({
            pathname: `/login/pc/findPassword`,
            query: {
                redirect_url,
            },
        });
    };
    // 将信令转换为token
    const voucherToToken = async (params: any) => {
        const data = await request({
            method: 'post',
            url: '/base-server-service/api/v1/user/newSecurityToken',
            data: params,
            noDataInterceptor: true,
        });
        return data;
    };

    // 获取客户端指纹
    const getDeviceFingerprint = () => {
        let fp = window.localStorage.getItem('DEVICE_FINGERPRINT');
        if(!fp){
            fp = generateUUID();
            window.localStorage.setItem('DEVICE_FINGERPRINT', fp);
        }
        return fp;
    }

    // 处理登录请求
    const handleLogin = useLockFn(async (authCode) => {
        const {
            privacyAgreementFlag,
            privacyPolicyVersion,
            userAgreementVersion,
        } = config;
        setTenantInfo({});
        form.validateFields().then((values: any) => {
            const { langue: lang, password, account } = values;
            let agreed = values.agreed;
            if (isCheckProtocol) {
                // 若隐私协议不展示，则不校验
                agreed = true;
            }
            setAgreedCheck(agreed);
            if (!agreed) return;
            // 终端类型：1：web端； 2：APP端； 3：API端； 不传则默认API登录。
            const clientType = 1;
            // 使用验证码次数记录+1
            setCodeUsedCount((count) => count + 1);
            // 登录类型 默认电脑滑块
            let loginParams = {
                ...values,
                imagesUuid: sliderVerifyCodeInfo?.uuid,
                xRatio: authCode,
                password: password ? encryptor.encrypt(md5(password)) : '',
                clientType,
                loginType: config.verificationCodeSwitch !== false ? '1' : '2',
            };
            // 平板验证码登录
            if (isDefaultLoginType && h5ValidateCode) {
                loginParams = {
                    ...values,
                    uuid: authParams.uuid,
                    password: password ? encryptor.encrypt(md5(password)) : '',
                    clientType,
                    loginType:
                        config.verificationCodeSwitch !== false ? null : '2',
                };
            }
            // 手机号登录
            if (!isDefaultLoginType) {
                loginParams = {
                    ...values,
                    type: 1,
                    clientType,
                };
            }
            if (privacyAgreementFlag) {
                loginParams = {
                    ...loginParams,
                    privacyPolicyVersion,
                    userAgreementVersion,
                };
            }
            // 客户端指纹
            loginParams.deviceFingerprint = getDeviceFingerprint();
            // 去除用户名的前后空格
            loginParams.account = account ? account.trim() : undefined;
            request
                .post(
                    '/base-server-service/api/v1/user/newSecurityLogin',
                    loginParams,
                    {
                        noDataInterceptor: true,
                    },
                )
                .then(async (rs: any) => {
                    const { data } = rs;
                    let resData;
                    if (data.code === 200) {
                        resData = data.data;
                        const { voucher } = resData;
                        window.localStorage.setItem('AUTH_VOUCHER', voucher);
                        window.localStorage.setItem('LANG', lang);
                        // 获取当前 URL 的 query 部分
                        const search = window.location.search;
                        // 使用 URLSearchParams 解析 query 参数
                        const queryParams = new URLSearchParams(search);
                        // 获取 redirect_url 参数
                        const redirect_url =
                            queryParams.get('redirect_url') || '';
                        if (redirect_url) {
                            // 解决安全问题，如果当前重定向的hostname与登录的hostname不同，则默认跳转到首页
                            // if (urlInfo.hostname !== window.location.hostname) {
                            //     window.location.replace('/');
                            //     return;
                            // }
                            const urlInfo = new URL(redirect_url);
                            const url = getRedirectUrl(urlInfo.hostname);
                            if (url === '/') {
                                window.location.replace('/');
                                return;
                            }
                            const urlQuery = qs.parse(
                                urlInfo.search.replace('?', ''),
                            );

                            urlQuery.auth_voucher = voucher;
                            urlQuery.lang = lang;
                            window.location.replace(
                                `${urlInfo.origin}${
                                    urlInfo.pathname
                                }?${qs.stringify(urlQuery)}${urlInfo.hash}`,
                            );
                            return;
                        } else {
                            // 没有redirect_url，则拼接voucher到url
                            window.location.replace(
                                `${window.location.origin}?auth_voucher=${voucher}`,
                            );
                        }
                    } else if (data.code === *********) {
                        const {
                            voucher,
                            emailNumber,
                            phoneNumber,
                            areaCode,
                            account,
                            userId,
                            verificationMode,
                            defaultMode,
                            tenantId,
                            allowTrustDevice,
                        } = data.data;
                        const query = history.location.query || {};
                        const redirect_url = query.redirect_url as string;
                        // 需要进行二次验证
                        sessionStorage.setItem(
                            'second-validate-voucher',
                            voucher,
                        );
                        const params = {
                            redirect_url: redirect_url,
                            mailNumber: emailNumber,
                            phoneNumber,
                            areaCode,
                            account,
                            userId,
                            verificationMode,
                            defaultMode: defaultMode + '',
                            tenantId,
                            allowTrustDevice,
                        };
                        history.push({
                            pathname: '/login/pc/secondValidate',
                            query: params,
                        });
                    } else if (data.code === 3118004) {
                        // 状态吗  3118004  密码过期或者第一次登陆需要修改密码
                        // 第一次登录修改密码或者密码过期 需要登录获取token
                        resData = data.data;
                        // 信令转为token 出错可以被捕获
                        const {
                            headers: { _token },
                        } = await voucherToToken({
                            voucher: resData.voucher,
                        });
                        window.localStorage.setItem(
                            'UPDATE_PASSWORD_TOKEN',
                            _token as string,
                        );
                        const query = history.location.query || {};
                        const redirect_url = query.redirect_url as string;
                        loginData = {
                            account: resData.account,
                            userId: resData.userId,
                            redirect_url: redirect_url,
                        };
                        Modal.info({
                            centered: true,
                            title: i18n.t(
                                '@base:@message__update_password.messagebox.title',
                                '',
                            ),
                            content: i18n.t(data.langKey),
                            okText: i18n.t(
                                '@base:@action__update_password.messagebox.confirm',
                                '',
                            ),
                            onOk: updatePassword,
                            cancelText: false,
                        });
                    } else if (data.code === *********) {
                        // 验证码失效/超时
                        handleCodeError(data);
                        // 关闭并重置滑块验证码位置
                        setupInterval();
                    } else if (data.code === 310104006) {
                        // 图片验证码错误的类型，此code只有图片验证码错误才会返回
                        // 提交登录后，接口返回结果，验证码错误，当次数超过最大次数时，需要重置定时器
                        // 【131399】需在接口返回验证码结果后才进行最大次数判断
                        if (getCodeUsedCount() >= SLIDER_CODE_USE_MAX_COUNT) {
                            // 提示失败次数过多，需要手动刷新，并重置定时器
                            setupInterval();
                            sliderVerifyRef.current?.reset();
                            sliderVerifyRef.current?.message({
                                type: 'error',
                                message: i18n.t(
                                    '@base:@message__login.verification.failed.many.times.message',
                                    '',
                                ),
                                autoCloseTime: 0,
                            });
                        } else {
                            // 验证码错误
                            handleCodeError(data);
                            // 验证码错误，刷新重置验证码定时器，但是不刷新验证码，因为有最大错误次数，若更新验证码，会重置最大次数，不符合产品预期
                            setupInterval(false);
                        }
                    } else if (data.code === *********) {
                        // license过期错误码
                        setTenantInfo(data);
                        // 关闭并重置滑块验证码位置
                        errorReset(true);
                    } else {
                        setErrInfo(
                            tt(data.langKey, '', {
                                count: (data['errorVar'] || [])[0],
                            }),
                        );
                        // 关闭并重置滑块验证码位置
                        errorReset(true);
                    }
                })
                .catch((err: any) => {
                    setErrInfo(err.toString());
                    // 关闭并重置滑块验证码位置，但是不刷新验证码
                    errorReset();
                })
                .finally(() => {
                    setSliderDisable(false);
                });
        });
    });
    // 关闭并重置滑块验证码位置
    const errorReset = (sliderVerifyCode: boolean) => {
        // 关闭并重置滑块验证码位置
        if (isDefaultLoginType && !h5ValidateCode) {
            // 关闭并重置滑块验证码位置，重置图片验证码定时器
            sliderVerifyCode && setupInterval();
            setSliderVerifyCodeVisible(false);
            sliderVerifyRef.current?.reset();
        }
        // 平板时
        isDefaultLoginType &&
            h5ValidateCode &&
            config.verificationCodeSwitch &&
            refreshCode();
    };
    const agreenVisibleClick = () => {
        setAgreedCheck(true);
    };

    // 登录前
    function beforeLogin() {
        // 验证表单字段
        form.validateFields().then((values) => {
            if (isCheckProtocol) {
                // 若隐私协议不展示，则不校验
                isDefaultLoginType &&
                !h5ValidateCode &&
                config.verificationCodeSwitch !== false
                    ? setSliderVerifyCodeVisible(true)
                    : handleLogin();
                return;
            }
            const { agreed } = values;
            setAgreedCheck(agreed);
            if (!agreed) return;
            isDefaultLoginType &&
            !h5ValidateCode &&
            config.verificationCodeSwitch !== false
                ? setSliderVerifyCodeVisible(true)
                : handleLogin();
        });
    }

    const handlePhoneVerfiyClick = useLockFn(async () => {
        if (countdingDown) return;
        const {
            phoneNumber,
            areaCode,
            langue: lang,
        } = await form.validateFields(['phoneNumber', 'areaCode', 'langue']);
        try {
            await request.get('/base-server-service/api/v1/phone/verify', {
                params: {
                    areaCode,
                    phoneNumber,
                    bizType: 0,
                },
                headers: {
                    _langtype:
                        lang ||
                        window.localStorage.getItem('LOGIN_LANG') ||
                        'zh_CN',
                },
            });
            setCountDown((s) => s - 1);
            countDownTimer = setInterval(() => {
                setCountDown((s) => {
                    if (s === 1) {
                        countDownTimer && clearInterval(countDownTimer);
                        return countDownTotal;
                    } else {
                        return s - 1;
                    }
                });
            }, 1000);
        } catch (e) {
            message.error(e);
        }
    });

    const validatorAccount = (rule: any, value: any) => {
        // 86041定制版本需求：用户名仅限制不能包含"&__"字符串，其他字符均允许
        if (value && !/^(?!.*(&__|\{\s*})).*$/g.test(value)) {
            return Promise.reject(
                i18n.t(
                    '@base:@message__login.username.chat.verification.message',
                    '',
                ),
            );
        }
        return Promise.resolve();
    };

    // 隐私声明
    const readAgreement = (type) => {
        const openCustom = config[agreementMap[type]]?.switchFlag === 1;
        const fileType = config[agreementMap[type]]?.type; // 1 为文件(fileList)  2 为链接(fileLink)
        if (openCustom && fileType === CUSTOM_AGREEMENT_TYPE.LINK) {
            window.open(config[agreementMap[type]].fileLink, '_blank');
            return;
        }
        const languageCode = localStorage.getItem('LOGIN_LANG');
        const { fileList } = config[agreementMap[type]] || {};
        const pdfUrl = (fileList || []).find(
            (item) => item.langType === languageCode,
        )?.fileUrl;
        const hrefStr = window.location.href;
        const urlParams = new URL(hrefStr);
        const origin = urlParams?.origin;
        const href =
            origin +
            `/login/pc/legalAgreement?type=${type}&custom=${
                openCustom ? 1 : 0
            }&languageTypes=${JSON.stringify(languageTypes)}&initFileUrl=${
                pdfUrl ? encodeURIComponent(pdfUrl) : ''
            }`;
        window.open(href, '_blank');
    };

    const getVerifyCodeText = () => {
        if (countdingDown) {
            return i18n.t('@base:@message__login.phone.code.btn.countdown', {
                count: countdown,
            });
        }
        return i18n.t('@base:@message__login.phone.code.btn', '');
    };
    if (
        config === null ||
        window.langueCache[
            window.localStorage.getItem('LOGIN_LANG') || 'en_US'
        ] === 'loading'
    )
        return null;

    const showNetRecordItem = (type: string) => {
        if (type === 'police') {
            return config?.netFilingEnable && config.policeFilingEnable;
        } else {
            return config?.netFilingEnable && config.icpFilingEnable;
        }
    };
    const notShowNetRecord =
        [0, null].includes(config.netFilingEnable) ||
        ([0, null].includes(config.policeFilingEnable) &&
            [0, null].includes(config.icpFilingEnable));

    // 样式1、2、3、5、6 侧边中间
    // 样式4、7 屏幕中间
    const renderNetRecord = () => {
        return !notShowNetRecord ? (
            <div
                className={
                    FULL_SCREEN_CENTER_STYLES.includes(config.loginPageStyle) ||
                    illustrationStyle
                        ? 'net-record-screen-center'
                        : `net-record-container net-record-container-${config.loginPageStyle}`
                }
            >
                {showNetRecordItem('police') ? (
                    <a
                        href={`https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=${config.policeFilingNumber}`}
                        className="net-record-first-item"
                        target="_blank"
                    >
                        {config.policeFilingContent}
                    </a>
                ) : null}
                {showNetRecordItem('icp') ? (
                    <a
                        href="https://beian.miit.gov.cn"
                        target="_blank"
                        className="net-record-first-item"
                    >
                        {config.icpFilingContent}
                    </a>
                ) : null}
            </div>
        ) : null;
    };

    const getLoginMarginTop = () => {
        if (illustrationStyle) return 0;
        if (config.loginTitleStyle === loginTitleStyleEnum.MIX) {
            return [LOGINSTYLE4, LOGIN_STYLE7].includes(config.loginPageStyle)
                ? 110
                : 140;
        }
        return 200;
    };
    const expireError = () => (
        <div className="error-info">
            {i18n.t('@base:@return__tenant_expire_fail.renewal', {
                renewal: (
                    <span onClick={tenantRenewal} className="error-renewal">
                        {tt(
                            '@base:@message__tenant.expired.message.manual.renewal',
                            '手动续期',
                        )}
                    </span>
                ),
            })}
        </div>
    );

    const thirdAccountLogin = async (source) => {
        if (!isCheckProtocol) {
            const agreed = await form.getFieldValue('agreed');
            if (!agreed) {
                setAgreedCheck(agreed);
                return;
            }
        }
        // 获取当前 URL 的 query 部分
        const search = window.location.search;
        // 使用 URLSearchParams 解析 query 参数
        const queryParams = new URLSearchParams(search);
        // 获取 redirect_url 参数
        const redirectUrl = queryParams.get('redirect_url') || undefined;
        const baseUrl = window.location.origin;
        const loginPageUrl = baseUrl + window.location.pathname;
        const bindingPageUrl = baseUrl + '/login/pc/bindAccount';
        const params = {
            source,
            redirectUrl,
            loginPageUrl,
            bindingPageUrl,
        };
        if (config.privacyAgreementFlag) {
            params.userAgreementVersion = config.userAgreementVersion;
            params.privacyPolicyVersion = config.privacyPolicyVersion;
        }
        const { data: jumpData } = await bindThirdAccount(params, {
            noDataInterceptor: true,
        });
        if (jumpData.success && jumpData.code === 200) {
            window.location.href = jumpData.data;
        } else {
            message.error(i18n.t(jumpData.langKey, jumpData.message));
        }
    };

    function renderSignIn() {
        const marginTop = getLoginMarginTop();
        return (
            <div
                className={classNames('login-content-form', {
                    'login-content-form-4':
                        config.loginPageStyle === LOGINSTYLE4,
                })}
                style={{
                    marginTop,
                    display: sliderVerifyCodeVisible ? 'none' : 'block',
                }}
            >
                <LogoWithTitle
                    onHeightChange={(height) => setLogoTitleHeight(height)}
                    data={{
                        loginTitleStyle: config.loginTitleStyle,
                        loginPageStyle: config.loginPageStyle,
                        loginTitlePic: config.loginTitlePic,
                    }}
                />
                <Form
                    form={form}
                    onKeyPress={(e) => {
                        if (e.nativeEvent && e.nativeEvent.code === 'Enter') {
                            beforeLogin();
                        }
                    }}
                    onValuesChange={onValuesChange}
                >
                    {isDefaultLoginType ? (
                        <AFormItem
                            name="account"
                            rules={[
                                {
                                    required: true,
                                    message: tt(
                                        '@base:@message__login.required.message.username',
                                    ),
                                },
                                {
                                    validator: validatorAccount,
                                },
                            ]}
                        >
                            <Input
                                placeholder={i18n.t(
                                    '@base:@message__login.placeholder.username',
                                    '',
                                )}
                                size="large"
                                // style={{ width: 440 }}
                            />
                        </AFormItem>
                    ) : (
                        <AFormItem name="areaCode" initialValue={86}>
                            <Select
                                dropdownClassName="login-page-select-phone-dropdown"
                                // style={{ width: 440, height: 40 }}
                                getPopupContainer={() => {
                                    return areaCodeSelectRef.current;
                                }}
                            >
                                {areaCodes.map((item) => (
                                    <Option
                                        value={item.areaCode}
                                        key={item.areaCode}
                                    >
                                        +{item.areaCode}&nbsp;
                                        {i18n.t(
                                            `@i18n:@areaCode__${item.areaCode}`,
                                            item.countryName,
                                        )}
                                    </Option>
                                ))}
                            </Select>
                        </AFormItem>
                    )}
                    {/* 勿删，解决手机号下拉框缩放导致的错位问题 */}
                    <div
                        className="select-dropdown-popover"
                        ref={areaCodeSelectRef}
                    ></div>
                    {isDefaultLoginType ? (
                        <AFormItem
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    message: tt(
                                        '@base:@message__login.required.message.password',
                                    ),
                                },
                            ]}
                        >
                            <Input
                                type="password"
                                placeholder={i18n.t(
                                    '@base:@message__login.placeholder.password',
                                    '',
                                )}
                                size="large"
                                // style={{ width: 440 }}
                            />
                        </AFormItem>
                    ) : (
                        <FormItem noStyle dependencies={['areaCode']}>
                            {({ getFieldValue }) => {
                                const areaCode = getFieldValue('areaCode');
                                return (
                                    <AFormItem
                                        name="phoneNumber"
                                        rules={[
                                            {
                                                required: true,
                                                message: tt(
                                                    '@base:@message__login.placeholder.phone',
                                                ),
                                            },
                                            {
                                                pattern:
                                                    areaCode == 86
                                                        ? /^\d{11}$/
                                                        : /^\d{1,20}$/,
                                                message: tt(
                                                    '@base:@message__login.phone.rule',
                                                ),
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder={i18n.t(
                                                '@base:@message__login.placeholder.phone',
                                                '',
                                            )}
                                            size="large"
                                            // style={{ width: 440 }}
                                        />
                                    </AFormItem>
                                );
                            }}
                        </FormItem>
                    )}

                    <AFormItem name="langue">
                        <Select
                            dropdownClassName="login-page-select-dropdown"
                            // style={{ width: 440, height: 40 }}
                            onChange={(v) => {
                                window.localStorage.setItem('LOGIN_LANG', v);
                            }}
                            getPopupContainer={() => {
                                return languageSelectRef.current;
                            }}
                        >
                            {languageTypes.map((item: any) => (
                                <Option key={item.value} value={item.value}>
                                    {item.label}
                                </Option>
                            ))}
                        </Select>
                    </AFormItem>
                    {/* 勿删，解决语言下拉框缩放导致的错位问题 */}
                    <div
                        className="select-dropdown-popover"
                        ref={languageSelectRef}
                    ></div>
                    {isDefaultLoginType &&
                        h5ValidateCode &&
                        config.verificationCodeSwitch && (
                            <FormItem className="code-form-item">
                                <AFormItem
                                    name="authCode"
                                    noStyle
                                    rules={[
                                        {
                                            required: true,
                                            message: tt(
                                                '@base:@message__login.required.message.auth_code',
                                            ),
                                        },
                                        {
                                            min: 4,
                                            message: tt(
                                                '@base:@message__login.min.message.auth_code',
                                            ),
                                        },
                                        {
                                            max: 4,
                                            message: tt(
                                                '@base:@message__login.max.message.auth_code',
                                            ),
                                        },
                                        {
                                            pattern: /^[\d\w]+$/,
                                            message: tt(
                                                '@base:@message__login.format.message.auth_code',
                                            ),
                                        },
                                    ]}
                                >
                                    <Input
                                        autoComplete="off"
                                        placeholder={i18n.t(
                                            '@base:@message__login.placeholder.auth_code',
                                            '',
                                        )}
                                        maxLength={4}
                                        size="large"
                                        style={{ width: 325 }}
                                    />
                                </AFormItem>
                                <img
                                    ref={codeRef}
                                    onClick={refreshCode}
                                    className="code-img"
                                    src={codeSrc}
                                    style={{ cursor: 'pointer', marginLeft: 8 }}
                                />
                            </FormItem>
                        )}
                    {!isDefaultLoginType && (
                        <FormItem className="code-form-item">
                            <AFormItem
                                name="verifyCode"
                                noStyle
                                rules={[
                                    {
                                        required: true,
                                        message: tt(
                                            '@base:@message__login.required.message.auth_code',
                                        ),
                                    },
                                    {
                                        type: 'string',
                                        max: 6,
                                        message: tt(
                                            '@base:@message__login.max.message.phone.auth_code',
                                        ),
                                    },
                                    {
                                        pattern: /^[\d]+$/,
                                        message: tt(
                                            '@base:@message__login.format.message.auth_code',
                                        ),
                                    },
                                ]}
                            >
                                <Input
                                    autoComplete="off"
                                    maxLength={6}
                                    placeholder={i18n.t(
                                        '@base:@message__login.placeholder.auth_code',
                                        '',
                                    )}
                                    size="large"
                                    style={{ width: 325 }}
                                />
                            </AFormItem>
                            <span
                                className={classNames('phone-verfiy-code-btn', {
                                    processing: countdingDown,
                                })}
                                style={{ cursor: 'pointer', marginLeft: 8 }}
                                onClick={handlePhoneVerfiyClick}
                                title={getVerifyCodeText()}
                            >
                                {getVerifyCodeText()}
                            </span>
                        </FormItem>
                    )}
                    {protocolInfo.isShowProtocol ? (
                        <AFormItem name="agreed" valuePropName="checked">
                            <div className="agreement-container">
                                {protocolInfo.isCheckConfirm ? (
                                    <Tooltip
                                        visible={!agreedCheck}
                                        placement="bottomLeft"
                                        title={tt(
                                            '@base:@message__update_password.message.legal_greement',
                                        )}
                                    >
                                        <Checkbox
                                            className="agree-box"
                                            onChange={(v) => {
                                                setAgreedCheck(
                                                    v.target.checked,
                                                );
                                            }}
                                        >
                                            {i18n.t(
                                                '@base:@message__update_password.title.legal_greement',
                                                '',
                                            )}
                                        </Checkbox>
                                    </Tooltip>
                                ) : (
                                    <span>
                                        {' '}
                                        {i18n.t(
                                            '@base:@message__update_password.title.no_legal_greement',
                                            '',
                                        )}
                                    </span>
                                )}
                                {/* 协议和政策前后增加空格 */}{' '}
                                <a
                                    onClick={() =>
                                        readAgreement(LegalType.agree)
                                    }
                                >
                                    {i18n.t(
                                        '@base:@message__update_password.title.legal',
                                        '',
                                    )}
                                </a>{' '}
                                {i18n.t(
                                    '@base:@message__update_password.title.and',
                                    '',
                                )}{' '}
                                <a
                                    onClick={() =>
                                        readAgreement(LegalType.secret)
                                    }
                                >
                                    {i18n.t(
                                        '@base:@message__update_password.title.greement',
                                        '',
                                    )}
                                </a>{' '}
                            </div>
                        </AFormItem>
                    ) : null}
                    <Button
                        className="login-btn"
                        type="primary"
                        onClick={beforeLogin}
                    >
                        {tt('@base:@action__login.button')}
                    </Button>
                    {config.retrievePassword === 1 && isDefaultLoginType && (
                        <div className="forget-password">
                            <span onClick={findPassword}>
                                {i18n.t(
                                    '@base:@action__forget_password.title',
                                    '',
                                )}
                            </span>
                        </div>
                    )}

                    <div
                        className={classNames('login-type-switch', {
                            'has-two-type-switch':
                                config.phoneFlag && config.msLoginEnable,
                        })}
                    >
                        {config.phoneFlag ? (
                            <span
                                title={
                                    !isDefaultLoginType
                                        ? i18n.t(
                                              '@base:@message__login.type.account',
                                              '',
                                          )
                                        : i18n.t(
                                              '@base:@message__login.type.phone',
                                              '',
                                          )
                                }
                                onClick={() => {
                                    countDownTimer &&
                                        clearInterval(countDownTimer);
                                    form.resetFields([
                                        'account',
                                        'phoneNumber',
                                        'verifyCode',
                                        'authCode',
                                        'password',
                                    ]);
                                    setLoginType(isDefaultLoginType ? 1 : 0);
                                }}
                            >
                                {!isDefaultLoginType ? (
                                    <>
                                        <Icon component={UserIcon} />
                                        {i18n.t(
                                            '@base:@message__login.type.account',
                                            '',
                                        )}
                                    </>
                                ) : (
                                    <>
                                        <IconMobilePhone />
                                        {i18n.t(
                                            '@base:@message__login.type.phone',
                                            '',
                                        )}
                                    </>
                                )}
                            </span>
                        ) : null}
                        {config.msLoginEnable ? (
                            <span
                                title={i18n.t(
                                    '@base:@message__login.type.microsoft',
                                    '微软账号登录',
                                )}
                                onClick={() => {
                                    thirdAccountLogin('microsoft');
                                }}
                            >
                                <IconMicrosoftLogo />
                                {i18n.t(
                                    '@base:@message__login.type.microsoft',
                                    '微软账号登录',
                                )}
                            </span>
                        ) : null}
                    </div>
                </Form>
                {tenantInfo.code === ********* ? (
                    expireError()
                ) : (
                    <div className="error-info">{errInfo}</div>
                )}
            </div>
        );
    }
    const tenantRenewal = () => {
        setModalVisible(true);
    };

    // 手动点击滑块验证码刷新按钮
    const onRefresh = async () => {
        // 调用setupInterval函数重新设置定时器
        setupInterval();
    };
    // 请求图片验证码
    function getSliderVerifyCodeInfo() {
        if (sliderVerifyCodeLoading) return;
        setSliderVerifyCodeLoading(true);
        setSliderDisable(true);

        sliderVerifyRef.current?.message();
        sliderVerifyRef.current?.reset();
        request('/base-server-service/api/v1/authPicture/newPicture')
            .then((res) => {
                const { bigImage, smallImage, imagesUuid, yRatio } = res;
                // 获取图片验证码信息后，记录当前时间，当离开当前窗口再次回来时，判断时间
                documentVisibleTime.current = Date.now();
                setSliderVerifyCodeInfo({
                    backgroundImage: bigImage,
                    blockImage: smallImage,
                    offsetY: yRatio / 10,
                    uuid: imagesUuid,
                });
                // 验证码信息更新后，需要重置验证码使用次数
                setCodeUsedCount(0);
            })
            .catch((err) => {
                // eslint-disable-next-line no-console
                console.log(err);
            })
            .finally(() => {
                setSliderVerifyCodeLoading(false);
                setSliderDisable(false);
            });
    }

    // 验证码错误
    function handleCodeError(data, autoCloseTime) {
        sliderVerifyRef.current?.message({
            type: 'error',
            message: tt(data.langKey, '', data.message),
            autoCloseTime,
        });
        sliderVerifyRef.current?.reset();
        sliderVerifyRef.current?.shake();
    }

    // 完成验证码拼图
    function handleFinishSliderCode(v) {
        setSliderDisable(true);
        handleLogin(v);
    }

    // 滑块鼠标按下事件，标记用户有操作，停止定时器
    function onSliderMouseDown() {
        // 清除定时器，结束计时
        clearSliderVerifyTimer();
    }

    function renderSliderVerifyCode() {
        return (
            <div className="slider-verify-code-wrapper">
                <SliderVerifyCode
                    ref={sliderVerifyRef}
                    visible={sliderVerifyCodeVisible}
                    verifyCodeInfo={sliderVerifyCodeInfo}
                    onClose={() => setSliderVerifyCodeVisible(false)}
                    onRefresh={onRefresh}
                    onFinish={handleFinishSliderCode}
                    onSliderMouseDown={onSliderMouseDown}
                    sliderDisabled={sliderVerifyCodeLoading || sliderDisable}
                    loading={sliderVerifyCodeLoading}
                />
            </div>
        );
    }

    const getBackImage = () => {
        if (config.loginPageStyle === LOGINPAGESTYLE3 && bgImageUrl) {
            // 样式三 + 自定义背景图
            return (
                <div
                    className="login-page-style3"
                    // style={{ backgroundImage: `url(${bgImageUrl})` }}
                >
                    <BlurImg
                        img={{
                            url: bgImageUrl,
                            hash: bgImageHash,
                        }}
                    />
                </div>
            );
        } else {
            return config?.backGroundLoaded ? (
                <AnimationBg
                    bgImageUrl={bgImageUrl}
                    bgImageHash={bgImageHash}
                    loginPageStyle={config.loginPageStyle}
                />
            ) : null;
        }
    };

    const [baseLoginIllustrationHeight] = calcContainerHight({
        originalValue: [
            sliderVerifyCodeVisible
                ? BG_CONTAINER_HEIGHT
                : BG_WITH_LOGO_CONTAINER_HEIGHT,
        ],
        isAbroadStyle,
        logoTitleHeight,
        sliderVerifyCodeVisible,
    });
    const renewalOnCancel = () => {
        setModalVisible(false);
    };
    const renewalOnOk = () => {
        setModalVisible(false);
        setTenantInfo({});
        setErrInfo(null);
    };

    return (
        <div
            id="login-container"
            className="login"
            ref={loginRef}
            onClick={agreenVisibleClick}
        >
            {config?.backGroundLoaded ? (
                <div
                    className="login-video-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                    }}
                >
                    {getBackImage()}
                </div>
            ) : null}
            {config?.backGroundLoaded && !illustrationStyle ? (
                <div
                    className="login-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    <LoginFormBg
                        loginPageStyle={config.loginPageStyle}
                        loginTitleStyle={config.loginTitleStyle}
                        sliderVerifyCodeVisible={sliderVerifyCodeVisible}
                        logoTitleHeight={logoTitleHeight}
                    />
                </div>
            ) : null}
            {config?.backGroundLoaded &&
            (illustrationStyle ? illustrationUrl : true) ? (
                <div
                    className="login-content-wrapper"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {illustrationStyle &&
                    config.loginPageStyle === LOGINSTYLE1 ? (
                        <div className="illustration-login-style">
                            <div
                                className="illustration"
                                style={{
                                    height: baseLoginIllustrationHeight,
                                }}
                            >
                                <BlurImg
                                    img={{
                                        url: illustrationUrl,
                                        hash: illustrationHash,
                                    }}
                                />
                            </div>
                            <div
                                style={{ height: baseLoginIllustrationHeight }}
                                className="login-content login-new-style1"
                            >
                                {renderSignIn()}
                                {!illustrationStyle &&
                                    !FULL_SCREEN_CENTER_STYLES.includes(
                                        config.loginPageStyle,
                                    ) &&
                                    renderNetRecord()}
                                {sliderVerifyCodeVisible &&
                                    renderSliderVerifyCode()}
                            </div>
                        </div>
                    ) : (
                        <div>
                            <div
                                className={classNames('login-content', {
                                    'login-style7':
                                        config.loginPageStyle === LOGIN_STYLE7,
                                })}
                            >
                                {renderSignIn()}
                                {sliderVerifyCodeVisible &&
                                    renderSliderVerifyCode()}
                            </div>
                        </div>
                    )}
                </div>
            ) : null}
            {!illustrationStyle &&
                !FULL_SCREEN_CENTER_STYLES.includes(config.loginPageStyle) &&
                renderNetRecord()}
            {(FULL_SCREEN_CENTER_STYLES.includes(config.loginPageStyle) ||
                illustrationStyle) &&
                renderNetRecord()}
            <InterfaceCenter scale={scale} config={config} />
            <PointConfig scale={scale} config={config} />
            <TenantRenewalModal
                onCancel={renewalOnCancel}
                onOk={renewalOnOk}
                visible={modalVisible}
                tenantInfo={tenantInfo.data}
            />
        </div>
    );
};
// style2 width: 706px

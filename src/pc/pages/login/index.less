@import '../../style/common';
#root {
    height: 100%;
}

::-webkit-scrollbar {
    display: none !important;
}
@keyframes show-item {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
@keyframes hide-item {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 0;
    }
}
.text-fade-in {
    display: inline-block;
    animation: show-item 0.5s ease-in;
}
.login {
    position: absolute;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    // background-image: url(~@/assets/img_bg.png);
    background-repeat: no-repeat;
    // background-position: top left;
    background-size: 100% 100%;
    &-logo {
        width: 240px;
        height: 60px;
        margin: 64px 0 0 72px;
    }
    &-content {
        z-index: 10;
        display: flex;
        justify-content: center;
        width: 800px;
        color: #333;

        &-title {
            width: 452px;
            height: 101px;
        }
        &-form {
            position: relative;
            box-sizing: border-box;
            width: 440px;
            margin-top: 200px;
            // height: 550px;
            .login-form-logo {
                display: flex;
                justify-content: center;
                margin-top: 40px;
            }
            .login-form-title {
                width: 100%;
                margin-top: 24px;
                margin-bottom: 40px;
                color: rgba(0, 0, 0, 0.85);
                font-weight: 400;
                font-size: 36px;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
                text-align: center;
                .login-page-title();
            }

            .code-form-item {
                .poppy-form-item-control-input-content {
                    display: flex;
                    align-items: center;
                    .code-img {
                        width: 107px;
                        height: 40px;
                        border-radius: 2px;
                    }
                }
            }

            .phone-verfiy-code-btn {
                width: 107px;
                height: 40px;
                padding: 0 8px;
                overflow: hidden;
                color: #fff;
                font-size: 16px;
                line-height: 40px;
                white-space: nowrap;
                text-align: center;
                text-overflow: ellipsis;
                background: @main-color;
                border-radius: 2px;
                transition: all 0.3s;
                &::abroad {
                    height: 56px;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 56px;
                    border-radius: 8px;
                }
                &.processing {
                    color: @main-color;
                    background: #597ef726;
                    border: #597ef726;
                    cursor: no-drop !important;
                }
            }

            .error-info {
                height: 16px;
                margin-top: 12px;
                color: red;
                line-height: 16px;
                text-align: center;
                .error-renewal {
                    text-decoration: underline;
                    cursor: pointer;
                    div {
                        text-decoration: underline;
                    }
                }
            }
            .login-type-switch {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 16px;
                text-align: center;
                &::abroad {
                    margin-top: 24px;
                }
                & > span {
                    overflow: hidden;
                    color: @main-color;
                    font-size: 14px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    cursor: pointer;
                    transition: all 0.3s;
                    &:hover {
                        opacity: 1;
                    }
                    > span {
                        margin-right: 4px;
                    }
                }
            }
            .has-two-type-switch {
                justify-content: space-between;
                & > span {
                    max-width: calc(50% - 16px);
                }
            }

            .login-btn {
                width: 100%;
                height: 48px;
                color: #fff;
                font-weight: 700;
                font-size: 16px;
                font-family: SourceHanSansCN-Bold, sans-serif;
                letter-spacing: 0;
                text-align: center;
                // background: @main-color;
                border: none;
                border-radius: 2px;
                &::abroad {
                    height: 58px;
                    //margin-top: 16px;
                }
            }
            .forget-password {
                display: flex;
                justify-content: flex-end;
                margin-top: 16px;
                color: @main-color;
                span {
                    cursor: pointer;
                }
            }
            #agreed::abroad {
                height: auto;
            }
            .agreement-container {
                margin-top: 16px;
                color: rgba(0, 0, 0, 0.65);
                word-break: break-all;
                &::abroad {
                    margin-top: unset;
                }
                .poppy-checkbox-wrapper {
                    margin-right: 0;
                    color: rgba(0, 0, 0, 0.65);
                    > span {
                        padding-right: 0;
                    }
                }
                > a {
                    color: @main-color;
                }
            }
        }
        &-form-4 {
            margin-top: 200px;
        }
        .select-dropdown-popover {
            position: relative;
            top: -21px;
            width: 0px;
            height: 0px;
            .login-page-select-phone-dropdown {
                top: 0px !important;
            }
        }
        &-wrapper {
            position: relative;
        }
    }
    .slider-verify-code-wrapper {
        margin-top: 260px;
    }
    &-background-img {
        flex: 1;
        width: 100%;
        background: #ccc;
        background-repeat: no-repeat;
        background-size: cover;
        user-select: none;
        img {
            width: 100%;
            min-height: 100%;
        }
    }
    .login-bg {
        width: 100%;
        height: 100%;
    }
    .login-content-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        .login-style7 {
            margin-left: -40px;
            &::abroad {
                margin-left: 0;
            }
            .login-content-form,
            .slider-verify-code-wrapper {
                // margin-right: 80px;
                // &::abroad {
                //     margin-right: 0;
                // }
            }
            .slider-verify-code-wrapper {
                margin-top: 280px;
            }
        }
        .illustration-login-style {
            position: absolute;
            top: 50%;
            left: 50%;
            display: flex;
            flex-direction: row;
            border-radius: 2px;
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
            transform: translate3d(-50%, -58%, 0);
            transform: translate3d(-50%, -52%, 0);
            .illustration {
                width: 878px;
                height: 650px;
            }
            .login-new-style1 {
                width: 560px;
                height: 650px;
                background-color: #fff;
                .login-content-form {
                    margin-top: 0;
                }
                .slider-verify-code-wrapper {
                    margin-top: 74px;
                }
            }
        }
    }

    .login-page-style3 {
        position: absolute;
        right: 0;
        width: 1120px;
        // height:995px;
        height: 100%;
        background-repeat: no-repeat;
        background-size: cover;
    }
    .login-page-select-dropdown {
        width: 440px;
        &::abroad {
            height: unset;
        }
    }
    .net-record-container {
        position: absolute;
        bottom: 24px;
        max-width: 440px;
        .net-record-first-item {
            margin-right: 20px;
        }
    }
    .net-record-container-1,
    .net-record-container-2,
    .net-record-container-3 {
        left: 21%;
        transform: translateX(-50%);
    }
    .net-record-container-5,
    .net-record-container-6 {
        right: 21%;
        transform: translateX(50%);
    }
    .net-record-screen-center {
        position: absolute;
        bottom: 0;
        left: 50%;
        background-color: #ffffffa6;
        transform: translateX(-50%);
        a {
            line-height: 48px;
        }
    }
    a.net-record-first-item {
        margin-right: 20px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 400;
        font-size: 14px;
    }
    a:hover {
        color: rgba(0, 0, 0, 0.8);
    }
}

@import '../../style/common';
#root {
    height: 100%;
}

::-webkit-scrollbar {
    display: none !important;
}

.find-password {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &-content {
        position: absolute;
        z-index: 10;
        display: flex;
        justify-content: center;
        width: 800px;
        color: #333;

        &-title {
            width: 452px;
            height: 101px;
        }
        .select-dropdown-popover {
            position: relative;
            top: -21px;
            width: 0px;
            height: 0px;
            .login-page-select-phone-dropdown {
                top: 0px !important;
                height: 40px;
                &::abroad {
                    height: unset;
                }
            }
        }
        &-form {
            position: relative;
            box-sizing: border-box;
            width: 440px;
            margin-top: 256px;
            .find-password-form-title {
                width: 100%;
                margin-bottom: 40px;
                color: rgba(0, 0, 0, 0.85);
                font-weight: 400;
                font-size: 48px;
                letter-spacing: 0;
                text-align: center;
                .login-page-title();
            }
            &-message-1 {
                margin-bottom: 10px;
                &::abroad {
                    margin-bottom: 24px;
                    color: rgba(0, 0, 0, 0.45);
                }
            }
            &-message-2 {
                margin-bottom: 24px;
                &::abroad {
                    color: rgba(0, 0, 0, 0.45);
                }
                .high-light-phone-email {
                    color: @main-color;
                }
            }
            .poppy-form {
                &::abroad {
                    display: inline-block;
                    width: 100%;
                }
            }
            .reset-password-container {
                &::abroad {
                    form {
                        display: inline-block;
                        width: 100%;
                    }
                }
            }
            .find-way-card-container {
                .find-way-card-item {
                    display: flex;
                    width: 440px;
                    height: 110px;
                    margin-bottom: 24px;
                    border: 1px solid #00000026;
                    border-radius: 2px;
                    &::abroad {
                        height: 120px;
                        border-radius: 8px;
                    }
                    .left-icon {
                        display: block;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 110px;
                        height: 110px;
                        color: @main-color;
                        &::abroad {
                            height: 120px;
                        }
                    }
                    .phone-icon svg {
                        width: 54px;
                        height: 54px;
                    }
                    .mail-icon svg {
                        width: 40px;
                        height: 40px;
                    }
                    .right-content {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        width: 312px;
                        padding-right: 16px;
                        .find-way-card-title {
                            margin-bottom: 4px;
                            font-weight: 600;
                            font-size: 18px;
                        }
                        .find-way-card-text {
                            color: #00000073;
                            font-weight: 400;
                            font-size: 14px;
                            word-break: break-all;
                        }
                    }
                }
                .card-item-active {
                    background-color: #fff;
                    cursor: pointer;
                }
                .card-item-disable {
                    background: #0000000a;
                    cursor: no-drop;
                    .left-icon {
                        span > svg {
                            color: #00000040;
                        }
                    }
                }
            }
            .error-info {
                height: 16px;
                margin-top: 12px;
                color: red;
                line-height: 16px;
                text-align: center;
            }
            .update-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 48px;
                color: #fff;
                font-weight: 700;
                font-size: 16px;
                letter-spacing: 0;
                text-align: center;
                border: none;
                border-radius: 2px;
                &::abroad {
                    height: 56px;
                    margin-top: 24px;
                }
            }
            .verify-code-container {
                .auth-code-form-item {
                    display: flex;
                    margin-bottom: 0;
                    .poppy-form-item-control-input-content {
                        .input-auth-code {
                            flex: 1;
                            min-width: 212px;
                            margin-bottom: 24px;
                            &::abroad {
                                margin-bottom: 0;
                            }
                            .poppy-form-item-control-input-content {
                                max-height: 56px;
                                margin-right: 12px;
                            }
                        }
                        .custom-find-password-btn {
                            height: 40px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            transition: all 0.3s;
                            &::abroad {
                                height: 56px;
                            }
                            span {
                                display: inline;
                            }
                        }
                    }
                }
                .poppy-form-item-control-input-content {
                    display: flex;
                    justify-content: space-between;
                    .poppy-form-item-control-input-content {
                        width: 326px;
                    }
                }
                .disable-get-code {
                    color: @main-color;
                    background: #597ef726;
                    border: #597ef726;
                    cursor: no-drop;
                }
                .no-get-code {
                    margin-top: 24px;
                    color: #00000073;
                    font-weight: 400;
                    font-size: 14px;
                    &::abroad {
                        margin-top: 40px;
                    }
                }
            }
            .reset-password-success {
                text-align: center;
                .icon span > svg {
                    width: 56px;
                    height: 56px;
                    color: #52c41a;
                }
                .title {
                    margin: 16px 0 10px;
                    color: #000000d9;
                    font-weight: 500;
                    font-size: 18px;
                    &::abroad {
                        margin-top: 24px;
                        margin-bottom: 16px;
                        font-weight: 700;
                        font-size: 24px;
                        line-height: 32px;
                    }
                }
                .redirect-seconds {
                    margin-bottom: 21px;
                    color: #000000a6;
                    font-weight: 400;
                    font-size: 14px;
                    &::abroad {
                        margin-bottom: 40px;
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
            }
            .custom-find-password-input {
                height: 40px;
            }
            .poppy-input-affix-wrapper-focused {
                border-color: @main-color !important;
            }
            .poppy-input-affix-wrapper:hover {
                border-color: @main-color !important;
            }
        }
    }
    .login-style7 {
        margin-right: 80px;
        &::abroad {
            margin-right: 0;
        }
    }
    .illustration-login-style {
        position: absolute;
        top: 215px;
        left: 241px;
        display: flex;
        flex-direction: row;
        border-radius: 2px;
        box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
        .illustration {
            width: 878px;
            height: 650px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .login-new-style1 {
            width: 560px;
            height: 650px;
            margin-top: 0;
            padding: 74px 66px 0;
            background-color: #fff;
        }
    }
}
.find-password-bg {
    width: 100%;
    height: 100%;
}

.login-find-password-error-way-message {
    .poppy-message-custom-content {
        display: flex;
    }
}

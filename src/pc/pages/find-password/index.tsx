/*
 * @LastEditTime: 2024-07-31 14:50:23
 */
// @ts-nocheck
import { useState, useEffect, useRef, useMemo } from 'react';
import { history } from 'umi';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { Button, Form, Input, message, Modal, Select } from '@streamax/poppy';
import classNames from 'classnames';
import {
    getScale,
    getRuntimeLocales,
    queryLoginConfig,
    encryptors,
    validateAuthCode,
    validatePassword,
    getOffsetInfo,
} from '@/utils/common';
import AnimationBg from '../../component/AnimationBg';
import {
    IconMobilePhone,
    IconMailbox,
    IconSuccessFill,
    IconInformation,
    IconEyeClose,
    IconEyeOpen,
} from '@streamax/poppy-icons';
import './index.less';
import { useAsyncEffect } from '@streamax/hooks';
import LoginFormBg, { isRightLoginStyle } from '@/pc/component/LoginFormBg';
import {
    RETRIEVE_PASSWORD_TYPE,
    PHONE_AUTH_CODE_LENGTH,
    EMAIL_AUTH_CODE_LENGTH,
    HALF_HOUR,
    STEPS,
    BIZ_TYPE,
    COUNT_DOWN_TOTAL,
    REDIRECT_DOWN_TOTAL,
    RESPONSE_CODE,
    LOGINSTYLE1,
    LOGIN_STYLE7,
    STATE_UN_USE,
} from '../../../utils/constant';
import usePhoneEmailUniqueVerify from './hooks/usePhoneEmailUniqueVerify';
// @ts-ignore
import md5 from 'js-md5';
import {
    validatorNewPassword,
    validateEmailOrPhone,
    getEmailOwnRule,
} from '@/utils/validator';
import BlurImg from '@/pc/component/BlurImg';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';
interface LoginConfigProps {
    retrievePassword: number;
    retrievePasswordType: string;
    curUseImageId: string;
}
interface UserInfoProps {
    account: string;
    areaCode: string;
    phoneNumber: string;
    mailNumber: string;
}
const defaultLocales = require('../../../app.locale.json');

type FindWayType = 'phoneNumber' | 'mailNumber' | '';
const emailRules: any = [];
let countDownTimer: any = null;
// RSA 2048 加密
const encryptor = encryptors();
const { Option } = Select;

export default () => {
    const [scale, setScale] = useState(getScale());
    const lang = window.localStorage.getItem('LOGIN_LANG') || 'zh_CN';
    const chineseReg = /[\u4e00-\u9fa5]/g;

    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);

    const [state, setState] = useState(false);

    const [config, setConfig] = useState<any>(null);
    const [bgImageUrl, setBgImageUrl] = useState<string>('');
    const [bgImageHash, setBgImageHash] = useState<string>('');
    const [illustrationUrl, setIllustrationUrl] = useState<string>(''); // 当前使用的插图url
    const [illustrationHash, setIllustrationHash] = useState<string>(''); // 当前使用的插图url
    const [illustrationStyle, setIllustrationStyle] = useState<boolean>(false); // 是否使用插图模式
    const [countdown, setCountDown] = useState(COUNT_DOWN_TOTAL);
    const [redirectDown, setRedirectDown] = useState(REDIRECT_DOWN_TOTAL);
    const [findWay, setFindWay] = useState<FindWayType>('');
    const timeOutRef: any = useRef(); // 重置成功后跳转时间
    const [userInfo, setUserInfo] = useState<UserInfoProps>({
        mailNumber: '',
        phoneNumber: '',
        account: '',
        areaCode: Number(window.APP_CONFIG['find.password.areacode']),
    }); // 用户信息;
    const countdingDown = countdown < COUNT_DOWN_TOTAL;
    const [identifyInfo, setIdentifyInfo] = useState({
        userId: '',
        signal: '',
    });
    const [eyes, setEyes] = useState({
        password: false,
        confirmWord: false,
    });
    const [areaCodes, setAreaCodes] = useState<any[]>([]);

    // 手机和邮箱是否唯一验证
    const { isNoPEVerify, step, setStep } = usePhoneEmailUniqueVerify();

    const findWayItems = [
        {
            type: 'phoneNumber',
            title: tt('@base:@message__reset_password.validate.ways.phone'),
            icon: <IconMobilePhone />,
            textCode:
                '@base:@message__reset_password.find.way.card.context.phone',
            unbindTextCode: '@base:@message__reset_password.no.bind.phone',
        },
        {
            type: 'mailNumber',
            title: tt('@base:@name__second_validate.way.label.email'),
            icon: <IconMailbox />,
            textCode:
                '@base:@message__reset_password.find.way.card.context.email',
            unbindTextCode: '@base:@message__reset_password.no.bind.email',
        },
    ];
    const [pwdConfig, setPwdConfig] = useState();

    const query = history.location?.query || {};
    const redirect_url = query.redirect_url as string;

    const [FormStatus, setFormStatus] = useState<any>({});
    const areaCodeSelectRef = useRef(null);

    const getAreaCodes = async () => {
        try {
            const data = await request.get(
                '/base-server-service/api/v1/areacode/query',
            );
            setAreaCodes(data);
        } catch (e) {
            // message.error(e);
        }
    };

    useEffect(() => {
        if (findWay === 'mailNumber' && !emailRules.length) {
            const rule = getEmailOwnRule();
            emailRules.push(rule);
        }
        if (findWay === 'phoneNumber' && !areaCodes.length) {
            getAreaCodes();
            form.setFieldsValue({
                areaCode: Number(window.APP_CONFIG['find.password.areacode']),
            });
        }
    }, [findWay]);

    const validatePwd = (_: any, value: string) => {
        return validatePassword(value, pwdConfig);
    };
    // 跳转登陆
    const redirectToLogin = () => {
        clearTimeout(timeOutRef.current);
        const url = redirect_url
            ? `/login/pc/login?redirect_url=${redirect_url}`
            : '/login/pc/login';
        const loginUrl = `${location.origin}${url}`;
        window.localStorage.removeItem('AUTH_FIND_PASSWORD_TOKEN');
        window.location.href = loginUrl;
    };
    const resizeScale = () => {
        setScale(getScale());
    };
    useEffect(() => {
        window.addEventListener('resize', resizeScale);
        // 清除监听事件和sessionStorage
        return () => {
            if (sessionStorage.getItem('find-password-safety-time')) {
                // 监听了点击事件
                document.body.removeEventListener('click', handleSafetyClick, {
                    capture: true,
                });
                sessionStorage.removeItem('find-password-safety-time');
            }
            window.removeEventListener('resize', resizeScale);
            window.onpopstate = null;
        };
    }, []);
    useEffect(() => {
        if (step === STEPS.SHOW_FIND_WAY) {
            // 刷新回到第一步时，清除计时和点击事件
            if (sessionStorage.getItem('find-password-safety-time')) {
                // 监听了点击事件
                document.body.removeEventListener('click', handleSafetyClick, {
                    capture: true,
                });
                sessionStorage.removeItem('find-password-safety-time');
            }
        }
    }, [step]);
    // 判断是否身份验证过期
    const checkIsSafety = () => {
        const second = Date.parse(String(new Date())) / 1000;
        const oldTimeStr = sessionStorage.getItem('find-password-safety-time');
        if (!oldTimeStr) {
            // 判断是否有存储时间
            return true;
        }
        const oldTime = Number(oldTimeStr);
        const isSafety = second - oldTime <= HALF_HOUR;
        return isSafety;
    };
    // 过期跳转登陆页
    const expireGoLogin = () => {
        const url = redirect_url
            ? `/login/pc/login?redirect_url=${redirect_url}`
            : '/login/pc/login';
        window.location.replace(url);
        sessionStorage.removeItem('find-password-safety-time');
    };
    // 给body绑定点击事件
    const handleSafetyClick = (e: any) => {
        const isSafety = checkIsSafety();
        if (!isSafety) {
            document.body.removeEventListener('click', handleSafetyClick, {
                capture: true,
            });
            const timer = setTimeout(() => {
                expireGoLogin();
            }, 3000);
            e.stopImmediatePropagation();
            Modal.info({
                title: tt('@base:@message__update_password.messagebox.title'),
                icon: <IconInformation />,
                content: i18n.t(
                    '@base:@message__reset_password.process.end.message',
                    {
                        second: 3,
                    },
                ),
                onOk: () => {
                    expireGoLogin();
                    clearTimeout(timer);
                },
                okText: tt('@base:@action__reset_password.login.right.now'),
            });
        }
    };
    // 判断过期刷新
    const type = performance.getEntriesByType('navigation')[0]?.type;

    if (type === 'reload') {
        const isSafety = checkIsSafety();
        const safetyTime = sessionStorage.getItem('find-password-safety-time');
        if (!isSafety && safetyTime) {
            // 确保存了sessionStorage
            expireGoLogin();
        }
    }
    // 获取国际化词条
    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType) return;
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        setState(!state);
        // eslint-disable-next-line no-plusplus
    };
    // 获取登录策略
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        try {
            dataConfig = await queryLoginConfig();
            if (dataConfig?.curUseImageId) {
                const hash = (dataConfig.images || []).find(
                    (item) => item.imageId == dataConfig?.curUseImageId,
                )?.hash;
                setBgImageHash(hash);
                setBgImageUrl(dataConfig?.curUseImageUrl);
            }
            if (dataConfig?.curUseIllustrationId) {
                const hash = (dataConfig.illustration || []).find(
                    (item) => item.imageId == dataConfig?.curUseIllustrationId,
                )?.hash;
                setIllustrationHash(hash);
                setIllustrationUrl(dataConfig?.curUseIllustrationUrl);
            }
            setIllustrationStyle(dataConfig?.loginIllustration || false);
            dataConfig.backGroundLoaded = true;
        } catch (e) {
            dataConfig.backGroundLoaded = true;
        }
        setConfig(dataConfig);
        return dataConfig;
    };
    useAsyncEffect(async () => {
        const lng = window.localStorage.getItem('LOGIN_LANG') || 'en_US';
        const loginConfig = await getLoginConfig();
        await i18nInit(lng as string, loginConfig?.tenantId);
        document.title = i18n.t('@base:@name__find_password.title', '');
    }, []);

    // 发送验证码按钮文本
    const getVerifyCodeText = () => {
        if (countdingDown) {
            return i18n.t('@base:@message__login.phone.code.btn.countdown', {
                count: countdown,
            });
        }
        return i18n.t('@base:@message__login.phone.code.btn', '');
    };
    // 获取验证码
    const handleVerifyCodeClick = async (type: string) => {
        if (countdingDown) return;
        const { phoneOrEmail } = await form.validateFields(['phoneOrEmail']);
        const params = {
            bizType: BIZ_TYPE.FIND_PASSWORD,
            [type]: phoneOrEmail,
            account: userInfo?.account,
        };
        if (type === 'phoneNumber') {
            params.areaCode = userInfo.areaCode;
        }
        try {
            // 开始倒计时
            setCountDown((s) => s - 1);
            countDownTimer = setInterval(() => {
                setCountDown((s) => {
                    if (s === 1) {
                        countDownTimer && clearInterval(countDownTimer);
                        return COUNT_DOWN_TOTAL;
                    } else {
                        return s - 1;
                    }
                });
            }, 1000);
            let rs: any;
            if (type === 'phoneNumber') {
                rs = await request.get(
                    `/base-server-service/api/v1/phone/verify`,
                    {
                        params,
                        noDataInterceptor: true,
                        headers: {
                            _langtype: lang,
                        },
                    },
                );
            } else {
                rs = await request.post(
                    `/base-server-service/api/v1/email/verify`,
                    params,
                    {
                        noDataInterceptor: true,
                        headers: {
                            _langtype: lang,
                        },
                    },
                );
            }
            const errorNumberCode =
                findWay === 'mailNumber'
                    ? RESPONSE_CODE.NOT_GET_AUTH_CODE[0] // 输入的邮箱错误
                    : RESPONSE_CODE.NOT_GET_AUTH_CODE[1]; // 输入的电话错误
            if (rs.data.code === errorNumberCode) {
                // 错误提示信息
                const msg =
                    findWay === 'mailNumber'
                        ? tt('@base:@message__reset_password.input.right.email')
                        : tt(
                              '@base:@message__reset_password.input.right.phone',
                          );
                setFormStatus({
                    ...FormStatus,
                    phoneOrEmailStatus: 'error',
                    phoneOrEmailHelp: msg,
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            }
            if (rs.data.code === RESPONSE_CODE.MAX_TIMES) {
                message.error({
                    content: tt(
                        '@base:@message__get_validate.code.times.limit',
                    ),
                    className: 'login-find-password-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.NOT_GET_AUTH_CODE[2]) {
                // 输入错误邮箱、提示参数错误
                message.error({
                    content: tt(rs.data.langKey),
                    className: 'login-find-password-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.FAIL_SEND_SMS) {
                // 发送短信验证码失败
                message.error({
                    content: tt('@base:@return__phone_auth_send_error'),
                    className: 'login-find-password-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (
                [
                    RESPONSE_CODE.HANDLE_STOP_USER,
                    RESPONSE_CODE.AUTO_STOP_USER,
                ].includes(rs.data.code)
            ) {
                message.error({
                    content: tt(rs.data.langKey, ''),
                    className: 'login-find-password-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (
                [
                    RESPONSE_CODE.USER_EMAIL_REPEAT,
                    RESPONSE_CODE.USER_PHONE_NUMBER_REPEAT,
                ].includes(rs.data.code)
            ) {
                if (RESPONSE_CODE.USER_EMAIL_REPEAT === rs.data.code) {
                    message.error({
                        content: tt(
                            '@base:@message__get_repeat.code.fail.send.email.repeat',
                        ),
                        className: 'login-find-password-error-way-message',
                    });
                } else {
                    message.error({
                        content: tt(
                            '@base:@message__get_repeat.code.fail.send.phone.repeat',
                        ),
                        className: 'login-find-password-error-way-message',
                    });
                }
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else {
                setCountDown((s) => s - 1);
            }
        } catch (error) {
            message.error(error);
        }
    };
    // 验证输入完整的邮箱/电话格式是否正确
    const validateNumber = (_: any, value: string) => {
        return validateEmailOrPhone(_, value, findWay);
    };
    // 验证验证码格式
    const checkAuthCode = (_: any, value: string) => {
        return validateAuthCode(_, value, findWay);
    };
    // 重置错误状态
    const resetFormStatus = (status: string, help: string) => {
        setFormStatus({
            ...FormStatus,
            [status]: undefined,
            [help]: undefined,
        });
    };
    // 下一步
    const handleNext = async () => {
        if (step === STEPS.CHECK_ACCOUNT) {
            // 校验账户是否存在
            const { account } = await form.validateFields();
            setLoading(true);
            try {
                const params = {
                    account: account.trim(),
                };
                // 查询账户是否存在
                const { data } = await request.get(
                    `/base-server-service/api/v1/user/exists`,
                    {
                        params,
                        noDataInterceptor: true,
                    },
                );
                // 用户不存在。修改表单状态
                if (data.code === RESPONSE_CODE.USER_UNEXIST) {
                    // 用户不存在
                    setFormStatus({
                        ...FormStatus,
                        accountStatus: 'error',
                        accountHelp: tt(
                            '@base:@message__reset_password.check.account.exist',
                        ),
                    });
                    setLoading(false);
                    // TODO
                } else {
                    const info = data.data;
                    if (info?.state === STATE_UN_USE) {
                        // 停用
                        setFormStatus({
                            ...FormStatus,
                            accountStatus: 'error',
                            accountHelp: tt('@base:@return__120020075'),
                        });
                        setLoading(false);
                        return;
                    }
                    // 用户存在，存储用户信息，开始30分钟计时
                    setStep(step + 1);
                    info.mailNumber = info.emailNumber;
                    setUserInfo(info);
                    // 此刻开始30分钟有效期计时
                    document.body.addEventListener('click', handleSafetyClick, {
                        capture: true,
                    });
                    sessionStorage.setItem(
                        'find-password-safety-time',
                        String(Date.parse(String(new Date())) / 1000),
                    );
                    setLoading(false);
                }
            } catch (error) {
                setLoading(false);
            }
        }
        if (step === STEPS.VERIFY_IDENTITY) {
            // 带验证码进行身份验证
            const { phoneOrEmail, authCode } = await form.validateFields([
                'phoneOrEmail',
                'authCode',
            ]);
            const params: any = {
                verifyType: Number(RETRIEVE_PASSWORD_TYPE[findWay]),
                bizType: BIZ_TYPE.FIND_PASSWORD,
                number: phoneOrEmail,
                verifyCode: authCode,
                account: userInfo?.account?.trim(),
            };
            if (findWay === 'phoneNumber') {
                params.areaCode = userInfo.areaCode;
            }
            // 用用户信息和验证码进行安全验证
            const rs = await request.post(
                `/base-server-service/api/v1/user/security/verify`,
                params,
                {
                    noDataInterceptor: true,
                },
            );
            if (RESPONSE_CODE.NOT_GET_AUTH_CODE.includes(rs.data.code)) {
                // 未点击获取验证码、输入的邮箱、电话不匹配
                message.error({
                    content: tt('@base:@message__user_not.get.validate.code'),
                    className: 'login-find-password-error-way-message',
                });
                form.setFieldsValue({
                    authCode: undefined,
                });
            } else if (rs.data.code === RESPONSE_CODE.AUTH_CODE_ERROR) {
                // 验证码错误
                setFormStatus({
                    ...FormStatus,
                    codeStatus: 'error',
                    codeHelp: tt(
                        '@base:@message__reset_password.auth.code.error',
                    ),
                });
            } else if (rs.data.code === RESPONSE_CODE.GET_CODE_LIMIT) {
                setFormStatus({
                    ...FormStatus,
                    codeStatus: 'error',
                    codeHelp: tt('@base:@return__120020030'),
                });
            } else {
                const data = rs.data.data;
                if (data) {
                    // 存储验证信息，重置密码使用
                    setIdentifyInfo({
                        userId: data.userId,
                        signal: data.signal,
                    });
                    setStep(step + 1);
                    setLoading(false);
                    const {
                        includeSpecialChar,
                        includeUpperLowerChar,
                        maxDigit,
                        minDigit,
                    } = rs.data.data;
                    setPwdConfig({
                        includeSpecialChar,
                        includeUpperLowerChar,
                        maxDigit,
                        minDigit,
                    });
                } else {
                    setFormStatus({
                        ...FormStatus,
                        codeStatus: 'error',
                        codeHelp: tt(
                            '@base:@message__reset_password.auth.code.error',
                        ),
                    });
                    setLoading(false);
                }
            }
        }
        if (step === STEPS.RESET_PASSWORD) {
            // 重置密码
            const upwds = await form.validateFields([
                'newPassword',
                'confirmPassword',
            ]);
            const resetParams = {
                userId: identifyInfo.userId,
                signal: identifyInfo.signal,
                newPwd: encryptor.encrypt(md5(upwds.newPassword)),
            };
            try {
                // 重置密码
                const res = await request({
                    method: 'post',
                    url: '/base-server-service/api/v1/user/retrieve',
                    data: resetParams,
                });
                if (res) {
                    setStep(step + 1);
                    setLoading(false);
                }
            } catch (error) {
                message.error(error);
                setLoading(false);
            }
        }
    };

    // 计算登录框的左侧的位置只能左侧移动定位，右侧定位不生效
    const calculateLeftLocationDebounce = useMemo(() => {
        // 判断是否右侧布局，不是就返回0
        if (!isRightLoginStyle(config?.loginPageStyle)) return 0;
        const { width } = getOffsetInfo(document.body);
        const leftMax = width - 800 * scale > 0 ? width - 800 * scale : 0;
        return leftMax;
    }, [scale, config?.loginPageStyle]);
    useEffect(() => {
        if (step === STEPS.RESET_SUCCESS) {
            // 重置成功
            setRedirectDown((s) => s - 1);
            timeOutRef.current = setInterval(() => {
                setRedirectDown((s) => {
                    if (s === 1) {
                        timeOutRef.current && clearInterval(timeOutRef.current);
                        redirectToLogin();
                        return '';
                    } else {
                        return s - 1;
                    }
                });
            }, 1000);
        }
        // 回退到找回密码流程的第一步(验证账户是否存在页)
        const blackStep = isNoPEVerify
            ? STEPS.CHECK_ACCOUNT
            : STEPS.SHOW_FIND_WAY;
        if (step > blackStep && !window.onpopstate) {
            // 去掉第一步验证账号是否存在后原第二步变为第一步
            window.history.pushState(null, null, document.URL);
            window.onpopstate = function () {
                window.location.reload();
            };
        }
    }, [step]);

    // 第一步：校验账号是否存在
    const step1 = (
        <>
            <div className="find-password-content-form-message-1">
                {i18n.t('@base:@message__reset_password.input.account', '')}
            </div>
            <Form form={form} autoComplete="off">
                <AFormItem
                    name="account"
                    rules={[
                        {
                            validator: (_: any, value: string) => {
                                if (!value || value.trim() === '') {
                                    return Promise.reject(
                                        tt(
                                            '@base:@message__login.required.message.username',
                                        ),
                                    );
                                } else {
                                    return Promise.resolve();
                                }
                            },
                            message: tt(
                                '@base:@message__login.required.message.username',
                            ),
                        },
                    ]}
                    validateStatus={FormStatus.accountStatus}
                    help={FormStatus.accountHelp}
                >
                    <Input
                        placeholder={i18n.t(
                            '@base:@message__login.required.message.username',
                            '',
                        )}
                        maxLength={50}
                        allowClear
                        className="custom-find-password-input"
                        onChange={() =>
                            resetFormStatus('accountStatus', 'accountHelp')
                        }
                    />
                </AFormItem>
            </Form>

            <Button
                loading={loading}
                className="update-btn"
                type="primary"
                onClick={handleNext}
            >
                {tt('@base:@action__find_password.next.step')}
            </Button>
        </>
    );
    // 第二步：展示找回密码方式
    const step2 = (
        <>
            <div className="find-password-content-form-message-2">
                {i18n.t('@base:@message__reset_password.identity.confirm', '')}
            </div>
            <div className="find-way-card-container">
                {findWayItems.map((item) => {
                    return config?.retrievePasswordType?.includes(
                        // 绑定了电话/邮箱
                        RETRIEVE_PASSWORD_TYPE[item.type],
                    ) ? (
                        <div
                            className={`find-way-card-item ${
                                // 在管理后台开启了对应的找回方式
                                userInfo[item.type] || !isNoPEVerify
                                    ? 'card-item-active'
                                    : 'card-item-disable'
                            }`}
                            key={item.type}
                            onClick={() => {
                                (userInfo[item.type] || !isNoPEVerify) &&
                                    config?.retrievePasswordType.includes(
                                        RETRIEVE_PASSWORD_TYPE[item.type],
                                    ) &&
                                    chooseFindWay(item.type);
                            }}
                        >
                            <div
                                className={`left-icon ${
                                    item.type === 'mailNumber'
                                        ? 'mail-icon'
                                        : 'phone-icon'
                                }`}
                            >
                                {item.icon}
                            </div>
                            <div className="right-content">
                                <div className="find-way-card-title">
                                    {item.title}
                                </div>
                                {isNoPEVerify ? (
                                    <div className="find-way-card-text">
                                        {userInfo[item.type]
                                            ? i18n.t(item.textCode, {
                                                  [item.type]:
                                                      item.type ===
                                                      'phoneNumber'
                                                          ? userInfo[item.type]
                                                          : userInfo[item.type],
                                              })
                                            : i18n.t(item.unbindTextCode)}
                                    </div>
                                ) : null}
                            </div>
                        </div>
                    ) : null;
                })}
            </div>
        </>
    );
    const changeAreaCode = (value: number) => {
        setUserInfo({ ...userInfo, areaCode: value });
    };
    // 第三步：获取验证码验证
    const step3 = (
        <div className="verify-code-container">
            <div className="find-password-content-form-message-2">
                {findWay === 'phoneNumber'
                    ? i18n.t(
                          '@base:@message__reset_password.input.complete.phone',
                          '',
                      )
                    : i18n.t(
                          '@base:@message__reset_password.input.complete.email',
                          '',
                      )}{' '}
                {isNoPEVerify ? (
                    <>
                        &nbsp;
                        <span className="high-light-phone-email">
                            {findWay === 'phoneNumber'
                                ? userInfo.phoneNumber
                                : userInfo.mailNumber}
                        </span>{' '}
                        &nbsp;
                        {i18n.t(
                            '@base:@message__reset_password.use.to.identify',
                            '',
                        )}
                    </>
                ) : null}
            </div>
            <Form form={form}>
                {findWay === 'phoneNumber' ? (
                    <AFormItem name="areaCode" initialValue={86}>
                        <Select
                            dropdownClassName="login-page-select-phone-dropdown"
                            // style={{ width: 440, height: 40 }}
                            getPopupContainer={() => {
                                return areaCodeSelectRef.current;
                            }}
                            onChange={changeAreaCode}
                        >
                            {areaCodes.map((item) => (
                                <Option
                                    value={item.areaCode}
                                    key={item.areaCode}
                                >
                                    +{item.areaCode}&nbsp;
                                    {i18n.t(
                                        `@i18n:@areaCode__${item.areaCode}`,
                                        item.countryName,
                                    )}
                                </Option>
                            ))}
                        </Select>
                    </AFormItem>
                ) : null}
                {/* 勿删，解决手机号下拉框缩放导致的错位问题 */}
                <div
                    className="select-dropdown-popover"
                    ref={areaCodeSelectRef}
                ></div>
                <Form.Item
                    validateFirst
                    name="phoneOrEmail"
                    rules={[
                        ...emailRules,
                        {
                            required: true,
                            validator: validateNumber,
                        },
                    ]}
                    validateStatus={FormStatus.phoneOrEmailStatus}
                    help={FormStatus.phoneOrEmailHelp}
                >
                    <Input
                        placeholder={
                            findWay === 'phoneNumber'
                                ? i18n.t(
                                      '@base:@message__login.placeholder.phone',
                                      '',
                                  )
                                : i18n.t(
                                      '@base:@message__reset_password.placeholder.email',
                                      '',
                                  )
                        }
                        maxLength={50}
                        className="custom-find-password-input"
                        allowClear
                        onChange={() =>
                            resetFormStatus(
                                'phoneOrEmailStatus',
                                'phoneOrEmailHelp',
                            )
                        }
                    />
                </Form.Item>
                <Form.Item className="auth-code-form-item">
                    <Form.Item
                        validateFirst
                        name="authCode"
                        rules={[
                            {
                                validator: checkAuthCode,
                            },
                        ]}
                        className="input-auth-code"
                        validateStatus={FormStatus.codeStatus}
                        help={FormStatus.codeHelp}
                    >
                        <Input
                            placeholder={i18n.t(
                                '@base:@message__login.placeholder.auth_code',
                                '',
                            )}
                            maxLength={
                                findWay === 'phoneNumber'
                                    ? PHONE_AUTH_CODE_LENGTH
                                    : EMAIL_AUTH_CODE_LENGTH
                            }
                            allowClear
                            className="custom-find-password-input"
                            onChange={() =>
                                resetFormStatus('codeStatus', 'codeHelp')
                            }
                        />
                    </Form.Item>
                    <Button
                        size="large"
                        type="primary"
                        className={`custom-find-password-btn ${
                            countdingDown ? 'disable-get-code' : ''
                        }`}
                        onClick={() => handleVerifyCodeClick(findWay)}
                        title={getVerifyCodeText()}
                    >
                        {getVerifyCodeText()}
                    </Button>
                </Form.Item>
                <Button
                    size="large"
                    loading={loading}
                    className="update-btn"
                    type="primary"
                    onClick={handleNext}
                >
                    {tt('@base:@action__find_password.next.step')}
                </Button>
            </Form>
            <div className="no-get-code">
                {tt('@base:@message__reset_password.no.verification.code')}
            </div>
        </div>
    );
    // 第四步：重置密码
    const step4 = (
        <div className="reset-password-container">
            <Form form={form}>
                <AFormItem
                    validateFirst
                    name="newPassword"
                    rules={[
                        {
                            required: true,
                            message: tt(
                                '@base:@message__update_password.placeholder.new_password',
                            ),
                        },
                        {
                            validator: validatePwd,
                        },
                    ]}
                    getValueFromEvent={(e) => {
                        const value = e.target.value;
                        return value.replace(chineseReg, '');
                    }}
                >
                    <Input
                        allowClear
                        placeholder={i18n.t(
                            '@base:@message__update_password.placeholder.new_password',
                            '',
                        )}
                        maxLength={20}
                        className="custom-find-password-input"
                        type={eyes.password ? 'text' : 'password'}
                        suffix={
                            <span
                                style={{ cursor: 'pointer' }}
                                onMouseEnter={() =>
                                    setEyes({ ...eyes, password: true })
                                }
                                onMouseLeave={() =>
                                    setEyes({ ...eyes, password: false })
                                }
                            >
                                {eyes.password ? (
                                    <IconEyeOpen />
                                ) : (
                                    <IconEyeClose />
                                )}
                            </span>
                        }
                    />
                </AFormItem>
                <AFormItem
                    name="confirmPassword"
                    rules={[
                        {
                            required: true,
                            message: tt(
                                '@base:@message__reset_password.placeholder.input.password.again',
                            ),
                        },
                        validatorNewPassword,
                    ]}
                    getValueFromEvent={(e) => {
                        const value = e.target.value;
                        return value.replace(chineseReg, '');
                    }}
                >
                    <Input
                        allowClear
                        placeholder={i18n.t(
                            '@base:@message__reset_password.placeholder.input.password.again',
                            '',
                        )}
                        maxLength={20}
                        className="custom-find-password-input"
                        type={eyes.confirmWord ? 'text' : 'password'}
                        suffix={
                            <span
                                style={{ cursor: 'pointer' }}
                                onMouseEnter={() =>
                                    setEyes({ ...eyes, confirmWord: true })
                                }
                                onMouseLeave={() =>
                                    setEyes({ ...eyes, confirmWord: false })
                                }
                            >
                                {eyes.confirmWord ? (
                                    <IconEyeOpen />
                                ) : (
                                    <IconEyeClose />
                                )}
                            </span>
                        }
                    />
                </AFormItem>
            </Form>
            <Button
                loading={loading}
                className="update-btn"
                type="primary"
                onClick={handleNext}
            >
                {tt('@base:@action__update_password.messagebox.confirm')}
            </Button>
        </div>
    );
    // 第五步：重置成功
    const step5 = (
        <div className="reset-password-success">
            <div className="icon">
                <IconSuccessFill />
            </div>
            <div className="title">
                {i18n.t('@base:@message__reset_password.success', '')}
            </div>
            <div className="redirect-seconds">
                {redirectDown +
                    i18n.t(
                        '@base:@action__reset_password.redirect.login.seconds',
                        '',
                    )}
            </div>
            <Button
                loading={loading}
                className="update-btn"
                type="primary"
                onClick={redirectToLogin}
            >
                {tt('@base:@action__reset_password.login.right.now')}
            </Button>
        </div>
    );
    // 选择找回方式
    const chooseFindWay = (chooseType: string) => {
        setStep(step + 1);
        setFindWay(chooseType);
    };
    // 找回密码操作区内容
    const getMainContent = () => {
        switch (step) {
            case STEPS.CHECK_ACCOUNT:
                return step1;
            case STEPS.SHOW_FIND_WAY:
                return step2;
            case STEPS.VERIFY_IDENTITY:
                return step3;
            case STEPS.RESET_PASSWORD:
                return step4;
            case STEPS.RESET_SUCCESS:
                return step5;
            default:
                return null;
        }
    };

    const getTitle = () => {
        switch (step) {
            case STEPS.CHECK_ACCOUNT:
            case STEPS.SHOW_FIND_WAY:
            case STEPS.VERIFY_IDENTITY:
                return tt('@base:@name__find_password.title');
            case STEPS.RESET_PASSWORD:
                return tt('@base:@name__reset_password.title');
            default:
                return null;
        }
    };

    return (
        <div id="find-password-container" className="find-password">
            {config?.backGroundLoaded ? (
                <div
                    className="find-password-video-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                    }}
                >
                    <AnimationBg
                        bgImageUrl={bgImageUrl}
                        bgImageHash={bgImageHash}
                        loginPageStyle={config?.loginPageStyle}
                    />
                </div>
            ) : null}
            {config?.backGroundLoaded && !illustrationStyle ? (
                <div
                    className="find-password-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    <LoginFormBg loginPageStyle={config?.loginPageStyle} />
                </div>
            ) : null}
            {/* {backGroundLoaded ? (
                <>
                    <AnimationBg bgImageUrl={bgImageUrl} />
                    <LoginFormBg />
                </>
            ) : null} */}
            {config?.backGroundLoaded &&
            (illustrationStyle ? illustrationUrl : true) ? (
                <div
                    className="update-password-wrapper"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {illustrationStyle &&
                    config.loginPageStyle === LOGINSTYLE1 ? (
                        <div className="illustration-login-style">
                            <div className="illustration">
                                <BlurImg
                                    img={{
                                        url: illustrationUrl,
                                        hash: illustrationHash,
                                    }}
                                />
                            </div>
                            <div className="find-password-content-form login-new-style1">
                                <div className="find-password-form-title">
                                    {getTitle()}
                                </div>
                                {getMainContent()}
                            </div>
                        </div>
                    ) : (
                        <div className="find-password-content">
                            <div
                                className={classNames(
                                    'find-password-content-form',
                                    {
                                        'login-style7':
                                            config?.loginPageStyle ===
                                            LOGIN_STYLE7,
                                    },
                                )}
                            >
                                <div className="find-password-form-title">
                                    {getTitle()}
                                </div>

                                {getMainContent()}
                            </div>
                        </div>
                    )}
                </div>
            ) : null}
        </div>
    );
};

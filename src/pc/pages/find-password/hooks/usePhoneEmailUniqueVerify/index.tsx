import { useAsyncEffect, useSetState } from '@streamax/hooks';
import request from '@/utils/request';
import { STEPS } from '@/utils/constant';
import useUrlState from '@ahooksjs/use-url-state';
import { useState } from 'react';

enum SystemConfigLevelEnum {
    SYSTEM, // 系统级别
    TENANT, // 租户级别
    APP, // 应用级别
    TENANT_APP, // 租户、应用级别
}

export default () => {
    const [userVerifyInfo, setUserVerifyInfo] = useSetState<{
        isNoPEVerify: boolean;
        loadFinish: boolean;
    }>({
        isNoPEVerify: false,
        loadFinish: false,
    });
    const [step, setStep] = useState<Number>();

    useAsyncEffect(async () => {
        try {
            const data: any = await request.post(
                '/base-server-service/api/v1/system/config/query/not/filter/permission',
                {
                    paramList: [
                        {
                            level: SystemConfigLevelEnum.SYSTEM,
                            key: 'user.phone.email.uniqueness.verification',
                        },
                    ],
                },
            );
            const currentItemValue = (data || []).find(
                (item) =>
                    item.key === 'user.phone.email.uniqueness.verification',
            )?.value;
            const noVerify = JSON.parse(currentItemValue || '{}')?.enable == 0;
            setUserVerifyInfo({
                isNoPEVerify: noVerify,
            });
            setStep(noVerify ? STEPS.CHECK_ACCOUNT : STEPS.SHOW_FIND_WAY);
        } catch (err) {
            setStep(STEPS.SHOW_FIND_WAY);
            console.log('fetch email and phone error', err);
        } finally {
            setUserVerifyInfo({
                loadFinish: true,
            });
        }
    }, []);

    return {
        ...userVerifyInfo,
        step,
        setStep,
    };
};

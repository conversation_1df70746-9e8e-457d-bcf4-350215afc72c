@main-color: #597ef7;
.login-agreement {
    padding: 0 220px;
    overflow: auto;
    .language-select-wraper {
        // 处理海外风格强制生效影响
        .poppy-select-selector {
            height: 40px !important;
        }
        position: absolute;
        top: 20px;
        right: 40px;
    }
    &-container {
        margin-top: 143px;
        &-pdf {
            font-size: 0;
        }
        &-item {
            height: 100vh;
            > iframe {
                height: 100%;
            }
        }
    }
    .poppy-select {
        position: relative;
    }
    .poppy-select:not(.poppy-select-disabled):hover .poppy-select-selector {
        border-color: @main-color;
    }
    // 下拉框hover focus颜色
    .login-content-form .poppy-select-selector:focus {
        border-color: @main-color;
    }
    .poppy-select-selector:focus,
    .poppy-select-selector:focus-within {
        border: 1px solid @main-color !important;
    }
    .poppy-select-item {
        line-height: 26px !important;
    }
    .poppy-select-dropdown {
        .poppy-select-item-option--active,
        .poppy-select-item-option-selected {
            // color: #fff;
            background: @main-color !important;
            //color: #fff;
        }
    }
}

// body::-webkit-scrollbar{
//     display: none;
// }

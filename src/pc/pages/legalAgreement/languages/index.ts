/*
 * @LastEditTime: 2025-01-02 14:44:47
 */
/*
 * @LastEditTime: 2022-07-23 16:01:29
 */
import zh_CN_statement from './zh_CN/statement';
import zh_CN_user_greement from './zh_CN/userAgreement';

import en_US_statement from './en_US/statement';
import en_US_user_greement from './en_US/userAgreement';

import ar_EG_statement from './ar_EG/statement';
import ar_EG_user_greement from './ar_EG/userAgreement';

import de_DE_statement from './de_DE/statement';
import de_DE_user_greement from './de_DE/userAgreement';

import es_ES_statement from './es_ES/statement';
import es_ES_user_greement from './es_ES/userAgreement';

import fr_FR_statement from './fr_FR/statement';
import fr_FR_user_greement from './fr_FR/userAgreement';

import ja_JP_statement from './ja_JP/statement';
import ja_JP_user_greement from './ja_JP/userAgreement';

import pt_BR_statement from './pt_BR/statement';
import pt_BR_user_greement from './pt_BR/userAgreement';

import ru_RU_statement from './ru_RU/statement';
import ru_RU_user_greement from './ru_RU/userAgreement';

import th_TH_statement from './th_TH/statement';
import th_TH_user_greement from './th_TH/userAgreement';

import zh_HK_statement from './zh_HK/statement';
import zh_HK_user_greement from './zh_HK/userAgreement';

import vi_VN_statement from './vi_VN/statement';
import vi_VN_user_greement from './vi_VN/userAgreement';

import tr_TR_statement from './tr_TR/statement';
import tr_TR_user_greement from './tr_TR/userAgreement';

import sr_RS_statement from './sr_RS/statement';
import sr_RS_user_greement from './sr_RS/userAgreement';

import it_IT_statement from './it_IT/statement';
import it_IT_user_greement from './it_IT/userAgreement';

import tg_TJ_statement from './tg_TJ/statement';
import tg_TJ_user_greement from './tg_TJ/userAgreement';

export type languageProps = {
    zh_CN_statement: string;
    zh_CN_user_greement: string;
    en_US_statement: string;
    en_US_user_greement: string;
    ar_EG_statement: string;
    ar_EG_user_greement: string;
    de_DE_statement: string;
    de_DE_user_greement: string;
    es_ES_statement: string;
    es_ES_user_greement: string;
    fr_FR_statement: string;
    fr_FR_user_greement: string;
    ja_JP_statement: string;
    ja_JP_user_greement: string;
    pt_BR_statement: string;
    pt_BR_user_greement: string;
    ru_RU_statement: string;
    ru_RU_user_greement: string;
    th_TH_statement: string;
    th_TH_user_greement: string;
    zh_HK_statement: string;
    zh_HK_user_greement: string;
    vi_VN_statement: string;
    vi_VN_user_greement: string;
    tr_TR_statement: string;
    tr_TR_user_greement: string;
    sr_RS_statement: string;
    sr_RS_user_greement: string;
    it_IT_statement: string;
    it_IT_user_greement: string;
    tg_TJ_statement: string;
    tg_TJ_user_greement: string;
};
export const allLanguages: languageProps = {
    zh_CN_statement,
    zh_CN_user_greement,
    en_US_statement,
    en_US_user_greement,
    ar_EG_statement,
    ar_EG_user_greement,
    de_DE_statement,
    de_DE_user_greement,
    es_ES_statement,
    es_ES_user_greement,
    fr_FR_statement,
    fr_FR_user_greement,
    ja_JP_statement,
    ja_JP_user_greement,
    pt_BR_statement,
    pt_BR_user_greement,
    ru_RU_statement,
    ru_RU_user_greement,
    th_TH_statement,
    th_TH_user_greement,
    zh_HK_statement,
    zh_HK_user_greement,
    vi_VN_statement,
    vi_VN_user_greement,
    tr_TR_statement,
    tr_TR_user_greement,
    sr_RS_statement,
    sr_RS_user_greement,
    it_IT_statement,
    it_IT_user_greement,
    tg_TJ_statement,
    tg_TJ_user_greement,
};

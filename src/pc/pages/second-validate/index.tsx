// @ts-nocheck
import { useState, useEffect, useRef, useMemo } from 'react';
import { history } from 'umi';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { Form, Input, Select, Button, Checkbox, message } from '@streamax/poppy';
import { IconVerificationCodeFill } from '@streamax/poppy-icons';
import {
    getScale,
    getRuntimeLocales,
    queryLoginConfig,
    validateAuthCode,
    getOffsetInfo,
} from '@/utils/common';
import AnimationBg from '../../component/AnimationBg';
import { useAsyncEffect } from '@streamax/hooks';
import qs from 'querystring';
import LoginFormBg, { isRightLoginStyle } from '@/pc/component/LoginFormBg';
import {
    PHONE_AUTH_CODE_LENGTH,
    EMAIL_AUTH_CODE_LENGTH,
    RETRIEVE_PASSWORD_TYPE,
    COUNT_DOWN_TOTAL,
    BIZ_TYPE,
    RESPONSE_CODE,
    LOGINSTYLE1,
    LOGIN_STYLE7,
} from '../../../utils/constant';
import classNames from 'classnames';
import './index.less';
import { getEmailOwnRule } from '@/utils/validator';
import BlurImg from '@/pc/component/BlurImg';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';
import OTPConfirmModal from "@/pc/pages/second-validate/components/OTPConfirmModal";
import {isNil} from "@streamax/poppy/es/starry-tree-select/components/rc-tree-select/src/utils/valueUtil";
import {getUserInfo} from "@/service/login";
interface UserInfoProps {
    phoneNumber: string;
    mailNumber: string;
    account: string;
    areaCode: string;
    userId: string;
    allowTrustDevice: boolean; // 是否配置勾选了允许信任设备
    tenantId;
}

type FindWayProps = 'mailNumber' | 'phoneNumber' | 'OTPCode';
const defaultLocales = require('../../../app.locale.json');
const emailRules = [];
let countDownTimer: any = null;
export default () => {
    const [scale, setScale] = useState(getScale());
    const lang = window.localStorage.getItem('LOGIN_LANG') || 'zh_CN';
    const languageSelectRef = useRef(null);

    const [userInfo, setUserInfo] = useState<UserInfoProps>(); // 登陆成功后返回的用户信息
    useEffect(() => {
        let info: any = history.location.query;
        info = {
            ...info,
            areaCode: info?.areaCode === 'null' ? null : info?.areaCode,
        };
        setUserInfo(info);
    }, []);

    const [form] = Form.useForm();

    const [state, setState] = useState(false);
    const [config, setConfig] = useState<any>();

    const [illustrationUrl, setIllustrationUrl] = useState<string>(''); // 当前使用的插图url
    const [illustrationHash, setIllustrationHash] = useState<string>('');
    const [bgImageHash, setBgImageHash] = useState<string>(undefined);

    const [illustrationStyle, setIllustrationStyle] = useState<boolean>(false); // 是否使用插图模式
    const [findWay, setFindWay] = useState<FindWayProps>();
    const [countdown, setCountDown] = useState(COUNT_DOWN_TOTAL);
    const [success, setSuccess] = useState<boolean>(false);
    const [number, setNumber] = useState<string>(); // 未脱敏手机或邮箱
    const [validateCodeInfo, setValidateCodeInfo] = useState<any>({
        status: undefined,
        help: undefined,
    });
    const [OTPConfirmModalVisible, setOTPConfirmModalVisible] = useState<boolean>(false);
    const [emailNumber, setEmailNumber] = useState<string>();


    const optionsMap = {
        '0': {
            label:
                userInfo?.phoneNumber !== 'null' // 绑定了电话
                    ? `+${userInfo?.areaCode} ` + userInfo?.phoneNumber
                    : tt('@base:@name__second_validate.way.label.phone'),
            value: 'phoneNumber',
        },
        '1': {
            label:
                userInfo?.mailNumber !== 'null' // 绑定了邮箱
                    ? userInfo?.mailNumber
                    : tt('@base:@name__second_validate.way.label.email'),
            value: 'mailNumber',
        },
        '2': {
            label: tt('@base:@name__second_validate.way.label.OTP'),
            value: 'OTPCode',
        },
    };
    const validateOptions = [];
    for (const key in optionsMap) {
        if (userInfo?.verificationMode?.includes(key)) {
            validateOptions.push(optionsMap[key]);
        }
    }

    useAsyncEffect(async () => {
        if ((findWay === 'OTPCode' || findWay === 'mailNumber') && !emailNumber && !isNil(userInfo?.userId) && !isNil(userInfo?.tenantId)){
            const email: string = await getUserInfoByField('email');
            setEmailNumber(email);
        }
    }, [findWay, userInfo]);

    useEffect(() => {
        let way: string | undefined;

        if (
            userInfo?.mailNumber !== 'null' &&
            userInfo?.verificationMode?.includes('1') &&
            userInfo?.defaultMode?.includes('1')
        ) {
            way = 'mailNumber';
        } else if (
            userInfo?.phoneNumber !== 'null' &&
            userInfo?.verificationMode?.includes('0') &&
            userInfo?.defaultMode?.includes('0')
        ) {
            way = 'phoneNumber';
        }else if (
            userInfo?.OTPCode !== 'null' &&
            userInfo?.verificationMode?.includes('2') &&
            userInfo?.defaultMode?.includes('2')
        ) {
            way = 'OTPCode';
        } else {
            way = undefined;
        }
        setFindWay(way);
        form.setFieldsValue({
            validateWay: way,
        });
    }, [userInfo]);

    const getUserInfoByField = async (field: string)=>{
        return await getUserInfo(
            {
                userId: userInfo?.userId,
                searchField: field,
            },
            {
                _tenantId: userInfo?.tenantId,
            },
        );
    };

    // 计算登录框的左侧的位置只能左侧移动定位，右侧定位不生效
    const calculateLeftLocationDebounce = useMemo(() => {
        // 判断是否右侧布局，不是就返回0
        if (!isRightLoginStyle(config?.loginPageStyle)) return 0;
        const { width } = getOffsetInfo(document.body);
        const leftMax = width - 800 * scale > 0 ? width - 800 * scale : 0;
        return leftMax;
    }, [scale, config?.loginPageStyle]);

    const countdingDown = countdown < COUNT_DOWN_TOTAL;

    useEffect(() => {
        if (findWay === 'mailNumber' && !emailRules.length) {
            const rule = getEmailOwnRule();
            emailRules.push(rule);
        }
    }, [findWay]);

    useEffect(() => {
        window.addEventListener('resize', () => {
            setScale(getScale());
        });
    }, []);
    // 获取国际化词条
    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType) return;
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        setState(!state);
        // eslint-disable-next-line no-plusplus
    };
    // 获取页面背景配置
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        try {
            dataConfig = await queryLoginConfig();
            if (dataConfig?.curUseImageId) {
                const hash = (dataConfig.images || []).find(
                    (item) => item.imageId == dataConfig?.curUseImageId,
                )?.hash;
                setBgImageHash(hash);
                dataConfig.bgImageUrl = dataConfig.curUseImageUrl;
            }
            if (dataConfig?.curUseIllustrationId) {
                const hash = (dataConfig.illustration || []).find(
                    (item) => item.imageId == dataConfig?.curUseIllustrationId,
                )?.hash;
                setIllustrationHash(hash);
                setIllustrationUrl(dataConfig?.curUseIllustrationUrl);
            }
            setIllustrationStyle(dataConfig?.loginIllustration || false);
            dataConfig.backGroundLoaded = true;
        } catch (e) {
            dataConfig.backGroundLoaded = true;
        }
        setConfig(dataConfig);
        return dataConfig;
    };
    useAsyncEffect(async () => {
        const lng = window.localStorage.getItem('LOGIN_LANG') || 'en_US';
        const loginConfig = await getLoginConfig();
        await i18nInit(lng as string, loginConfig?.tenantId);
        document.title = i18n.t('@base:@name__second_validate.pc.title', '');
    }, []);

    // 验证验证码格式
    const checkAuthCode = (_: any, value: string) => {
        return validateAuthCode(_, value, findWay);
    };
    // 发送验证码按钮文本
    const getVerifyCodeText = () => {
        if (countdingDown) {
            return i18n.t('@base:@message__login.phone.code.btn.countdown', {
                count: countdown,
            });
        }
        return i18n.t('@base:@message__login.phone.code.btn', '');
    };
    // 获取验证码
    const handleVerifyCodeClick = async (type: string) => {
        if (countdingDown) return;
        await form.validateFields(['validateWay']);
        setCountDown((s) => s - 1);
        countDownTimer = setInterval(() => {
            setCountDown((s) => {
                if (s === 1) {
                    countDownTimer && clearInterval(countDownTimer);
                    return COUNT_DOWN_TOTAL;
                } else {
                    return s - 1;
                }
            });
        }, 1000);
        let data: string;
        if (type === 'mailNumber' && emailNumber){
            data = emailNumber;
            setNumber(emailNumber);
        }else {
            data = await getUserInfoByField(type === 'mailNumber' ? 'email' : 'phoneNumber');
            setNumber(data);
        }
        const params = {
            bizType: BIZ_TYPE.SECOND_VALIDATE,
            [type]: data,
            account: userInfo?.account,
        };
        if (type === 'phoneNumber') {
            params.areaCode = userInfo?.areaCode;
        }
        let rs: any;
        try {
            findWay === 'phoneNumber'
                ? (rs = await request.get(
                      `/base-server-service/api/v1/phone/verify`,
                      {
                          params,
                          noDataInterceptor: true,
                          headers: {
                              _langtype: lang,
                          },
                      },
                  ))
                : (rs = await request.post(
                      `/base-server-service/api/v1/email/verify`,
                      params,
                      {
                          noDataInterceptor: true,
                          headers: {
                              _langtype: lang,
                          },
                      },
                  ));
            if (rs.data.code === RESPONSE_CODE.MAX_TIMES) {
                message.error({
                    content: tt(
                        '@base:@message__get_validate.code.times.limit',
                    ),
                    className: 'login-second-validate-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.FAIL_SEND_SMS) {
                message.error({
                    content: tt('@base:@message__get_auth.code.fail.send.sms'),
                    className: 'login-second-validate-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.NOT_GET_AUTH_CODE[0]) {
                message.error({
                    content: tt(rs.data.langKey),
                    className: 'login-second-validate-error-way-message',
                });
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            }
        } catch (error) {
            message.error(error);
        }
    };
    // 处理验证方式改变
    const handleWaySelect = (way: 'mailNumber' | 'phoneNumber') => {
        if (['null', null].includes(userInfo[way])) {
            const msg =
                way === 'mailNumber'
                    ? tt('@base:@message__second_validate.safety.user.email')
                    : tt('@base:@message__second_validate.safety.user.phone');
            message.warning({
                content: msg,
                className: 'login-second-validate-warn-way-message',
            });
            form.resetFields();
            return;
        }
        setFindWay(way);
    };
    // 安全验证
    const safetyValidate = async () => {
        const { authCode, allowTrustDevice } = form.getFieldsValue();
        await form.validateFields(['validateWay', 'authCode']);
        let params: any;
        if (findWay === 'OTPCode'){
            params = {
                verifyType: Number(RETRIEVE_PASSWORD_TYPE[findWay]),
                bizType: BIZ_TYPE.SECOND_VALIDATE, // 二次验证
                number: userInfo?.userId,
                verifyCode: authCode,
                account: userInfo?.account || 'streamax',
            };
        }else {
            const deviceFingerprint = window.localStorage.getItem('DEVICE_FINGERPRINT');
            const voucher = sessionStorage.getItem('second-validate-voucher');
            params = {
                verifyType: Number(RETRIEVE_PASSWORD_TYPE[findWay]),
                bizType: BIZ_TYPE.SECOND_VALIDATE, // 二次验证
                number,
                verifyCode: authCode,
                account: userInfo?.account || 'streamax',
                voucher,
                deviceFingerprint,
            };
            if (findWay === 'phoneNumber') {
                params.areaCode = userInfo?.areaCode;
            }
            if(userInfo?.allowTrustDevice) {
                params.allowTrustDevice = allowTrustDevice;
            }
        }
        request
            .post(`/base-server-service/api/v1/user/security/verify`, params, {
                noDataInterceptor: true,
            })
            .then(async (rs: any) => {
                const { data } = rs;
                if (data.code === 200) {
                    setSuccess(true);
                    // 跳转主页
                    goMainPage();
                } else if (data.code === RESPONSE_CODE.GET_CODE_LIMIT) {
                    setValidateCodeInfo({
                        status: 'error',
                        help: tt('@base:@return__120020030'),
                    });
                } else if (
                    findWay === 'OTPCode' && data.code !== RESPONSE_CODE.OTP_CODE_ERROR_MESSAGE
                ) {
                    setValidateCodeInfo({
                        status: 'error',
                        help: tt('@base:@message__second_validate.otp.validate.error'),
                    });
                }else if(data.code === RESPONSE_CODE.OTP_CODE_ERROR_MESSAGE){
                    message.warning({
                        content: tt(data.langKey),
                        className: 'login-second-validate-warn-way-message',
                    }
                    );
                }
                else {
                    setValidateCodeInfo({
                        status: 'error',
                        help: tt(
                            '@base:@message__reset_password.auth.code.error',
                        ),
                    });
                }
            });
    };
    // 按钮内容
    const getBtnText = () => {
        if (success) {
            return tt('@base:@message__second_validate.safety.success');
        } else {
            return tt('@base:@message__second_validate.safety.validate');
        }
    };
    // 获取跳转路径
    function getRedirectUrl(url: any) {
        const whiteList = ['localhost', '127.0.0.1'];
        let flag = false;
        whiteList.forEach((item) => {
            url.indexOf(item) > -1 && (flag = true);
        });
        if (flag) {
            return url;
        }
        if (url !== window.location.hostname) {
            return '/';
        }
        return url;
    }
    // 二次验证通过后跳转
    const goMainPage = () => {
        const voucher = sessionStorage.getItem('second-validate-voucher');
        window.localStorage.setItem('AUTH_VOUCHER', voucher);
        window.localStorage.setItem('LANG', lang);
        const query = history.location.query || {};
        const redirect_url = query.redirect_url as string;
        if (redirect_url && redirect_url !== 'undefined') {
            const urlInfo = new URL(redirect_url);
            const url = getRedirectUrl(urlInfo.hostname);
            if (url === '/') {
                window.location.replace(
                    `${window.location.origin}?auth_voucher=${voucher}`,
                );
                return;
            }
            const urlQuery = qs.parse(urlInfo.search.replace('?', ''));

            urlQuery.auth_voucher = voucher;
            urlQuery.lang = lang;
            window.location.replace(
                `${urlInfo.origin}${urlInfo.pathname}?${qs.stringify(
                    urlQuery,
                )}${urlInfo.hash}`,
            );
            return;
        }

        window.location.replace(
            `${window.location.origin}?auth_voucher=${voucher}`,
        );
    };
    // 验证方式
    const validateChooseWay = (_: any, value: string) => {
        if (!value) {
            return Promise.reject(
                tt('@base:@message__second_validate.choose.way.message'),
            );
        }
        return Promise.resolve();
    };
    // 重置验证码框状态
    const handleCodeChange = () => {
        setValidateCodeInfo({
            status: undefined,
            help: undefined,
        });
    };

    const renderContent = () => {
        return (
            <div
                className={classNames('second-validate-content-form', {
                    'login-style7': config?.loginPageStyle === LOGIN_STYLE7,
                })}
            >
                <div className="second-validate-content-form-title">
                    {tt('@base:@name__second_validate.pc.title')}
                </div>
                <p className="confirm-tip-message">
                    {findWay === 'OTPCode' ? tt('@base:@message__second_validate.otp.validate.title') : tt('@base:@name__second_validate.pc.tip.message')}
                </p>
                <Form layout="vertical" form={form}>
                    <AFormItem
                        name="validateWay"
                        rules={[
                            {
                                validator: validateChooseWay,
                                message: tt(
                                    '@base:@message__second_validate.choose.way.message',
                                ),
                            },
                        ]}
                    >
                        <Select
                            dropdownClassName="second-page-select-dropdown"
                            options={validateOptions}
                            // style={{ width: 440, height: 40 }}
                            getPopupContainer={() => {
                                return languageSelectRef.current;
                            }}
                            placeholder={
                                !userInfo?.mailNumber && !userInfo?.phoneNumber
                                    ? tt(
                                          '@base:@message__second_validate.choose.way.message',
                                      )
                                    : tt(
                                          '@base:@message__second_validate.choose.way.message',
                                      )
                            }
                            onSelect={handleWaySelect}
                        />
                    </AFormItem>
                    {/* 勿删，解决下拉框缩放导致的错位问题 */}
                    <div
                        className="second-select-dropdown-popover"
                        ref={languageSelectRef}
                    ></div>
                    <div className="auth-code-form-item">
                        <AFormItem
                            validateFirst
                            name="authCode"
                            rules={[
                                {
                                    validator: checkAuthCode,
                                },
                            ]}
                            validateStatus={validateCodeInfo.status}
                            help={validateCodeInfo.help}
                            className="auth-code-input"
                        >
                            <Input
                                placeholder={findWay === 'OTPCode' ? i18n.t('@base:@message__second_validate.otp.placeholder','') : i18n.t(
                                    '@base:@message__login.placeholder.auth_code',
                                    '',
                                )}
                                maxLength={
                                    findWay === 'phoneNumber'
                                        ? PHONE_AUTH_CODE_LENGTH
                                        : EMAIL_AUTH_CODE_LENGTH
                                }
                                allowClear
                                onChange={handleCodeChange}
                            />
                        </AFormItem>
                        {findWay === 'OTPCode' ? null :
                            <Button
                                type="primary"
                                className={`get-code-btn ${
                                    countdingDown ? 'disable-get-code' : ''
                                }`}
                                onClick={() => handleVerifyCodeClick(findWay)}
                                title={getVerifyCodeText()}
                            >
                                {getVerifyCodeText()}
                            </Button>
                        }

                    </div>
                    {userInfo?.allowTrustDevice && findWay !== "OTPCode" && (
                        <AFormItem
                            name="allowTrustDevice"
                            valuePropName="checked"
                            initialValue={false}
                            className="trust-checkbox-item"
                        >
                            <Checkbox>
                                {tt('@base:@message__login.check.trust.tip')}
                            </Checkbox>
                        </AFormItem>
                    )}
                    <Button
                        className={`update-btn ${
                            success ? 'validate-success' : ''
                        }`}
                        type="primary"
                        onClick={safetyValidate}
                    >
                        <p className="btn-content">
                            {success && (
                                <IconVerificationCodeFill className="safety-verify-icon" />
                            )}
                            {getBtnText()}
                        </p>
                    </Button>
                </Form>
                <p className="no-get-code">
                    {findWay === "OTPCode" && (
                        <span className={'otp-binding-tips'}>
                            <span>
                                {tt('@base:@message__second_validate.otp.tips.not.binding','')}
                            </span>
                            <a onClick={()=>{
                                if (!emailNumber){
                                    message.warning({
                                        content: tt('@base:@message__second_validate.otp.tips.not.binding.email'),
                                        className: 'login-second-validate-warn-way-message',
                                    });
                                }else {
                                    setOTPConfirmModalVisible(true);
                                }
                            }}>{
                                tt('@base:@message__second_validate.otp.tips.click.here','')}
                            </a>
                            <span>
                                {tt('@base:@message__second_validate.otp.tips.binding','')}
                            </span>
                        </span>
                        )}
                    <div>
                        {tt('@base:@message__reset_password.no.verification.code')}
                    </div>
                </p>
            </div>
        );
    };

    return (
        <div id="second-validate-container" className="second-validate">
            {config?.backGroundLoaded ? (
                <div
                    className="scend-validate-video-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                    }}
                >
                    <AnimationBg
                        bgImageUrl={config?.bgImageUrl}
                        bgImageHash={bgImageHash}
                        loginPageStyle={config?.loginPageStyle}
                    />
                </div>
            ) : null}
            {config?.backGroundLoaded && !illustrationStyle ? (
                <div
                    className="scend-validate-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    <LoginFormBg loginPageStyle={config?.loginPageStyle} />
                </div>
            ) : null}
            {config?.backGroundLoaded &&
            (illustrationStyle ? illustrationUrl : true) ? (
                <div
                    className="second-validate-wrapper"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {illustrationStyle &&
                    config.loginPageStyle === LOGINSTYLE1 ? (
                        <div className="illustration-login-style">
                            <div className="illustration">
                                <BlurImg
                                    img={{
                                        url: illustrationUrl,
                                        hash: illustrationHash,
                                    }}
                                />
                            </div>
                            <div className="second-validate-content login-new-style1">
                                {renderContent()}
                            </div>
                        </div>
                    ) : (
                        <div className="second-validate-content">
                            {renderContent()}
                        </div>
                    )}
                </div>
            ) : null}

            <OTPConfirmModal
                visible={OTPConfirmModalVisible}
                onClose={()=>{setOTPConfirmModalVisible(false);}}
                userInfo={userInfo}
                fullEmail={emailNumber}
                lang={lang}
            />
        </div>
    );
};

@import url('../../style/common');
#root {
    height: 100%;
}

::-webkit-scrollbar {
    display: none !important;
}
.second-page-select-dropdown {
    width: 440px !important;
    min-width: 440px !important;

    .poppy-select-item-option--active,
    .poppy-select-item-option-selected {
        //color: #fff;
        background: @main-color !important;
    }
}
.second-select-dropdown-popover {
    position: relative;
    top: -21px;
    width: 0px;
    height: 0px;
    .second-page-select-dropdown {
        top: 0px !important;
        left: 0px !important;
    }
}
.second-validate {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &-content {
        position: absolute;
        z-index: 10;
        display: flex;
        justify-content: center;
        width: 800px;
        margin-top: 256px;
        color: #333;
        &-form {
            position: relative;
            box-sizing: border-box;
            width: 440px;
            &.login-style7 {
                margin-left: -80px;
                &::abroad {
                    margin-left: 0;
                }
            }
            .update-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 48px;
                color: #fff;
                font-weight: 700;
                font-size: 16px;
                letter-spacing: 0;
                text-align: center;
                border: none;
                border-radius: 2px;
                margin-top: 40px;
                &::abroad {
                    height: 56px;
                    margin-top: 16px;
                }
                .btn-content {
                    display: flex;
                    justify-content: center;
                    margin: 8px auto;
                }
            }
            .validate-success {
                color: @main-color;
                background: rgba(89, 128, 248, 0.15);
                p {
                    margin: 8px auto;
                    span {
                        margin: 4px 8px 0 0;
                    }
                }
            }
            &-title {
                width: 452px;
                margin-bottom: 24px;
                color: #000000d9;
                font-weight: 400;
                font-size: 36px;
                text-align: center;
                .login-page-title();
                &::abroad {
                    margin-bottom: 40px;
                }
            }
            .confirm-tip-message {
                color: #000000d9;
                font-weight: 400;
                font-size: 14px;
                &::abroad {
                    color: rgba(0, 0, 0, 0.45);
                }
            }
            .auth-code-form-item {
                display: flex;
                gap: 12px;
                    .auth-code-input {
                        flex: 1;
                        .poppy-input-affix-wrapper-focused {
                            border-color: @main-color !important;
                        }
                        .poppy-input-affix-wrapper:hover {
                            border-color: @main-color !important;
                        }
                    }
                    .get-code-btn {
                        width: 102px !important;
                        height: 40px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        &::abroad {
                            width: 39% !important;
                            max-width: 196px;
                            height: 56px;
                        }
                        span {
                            display: inline;
                        }
                    }
                    .disable-get-code {
                        color: @main-color;
                        background: #597ef726;
                        border: #597ef726;
                        cursor: no-drop;
                    }
                }
            .no-get-code {
                margin-top: 24px;
                color: #00000073;
                font-weight: 400;
                font-size: 14px;
            }

            .trust-checkbox-item {
                .poppy-checkbox-wrapper {
                    height: auto;
                    align-items: flex-start;
                }
            }
        }
    }
    .scend-validate-bg {
        width: 100%;
        height: 100%;
    }
    .second-validate-wrapper {
        width: 100%;
        width: 100%;
        height: 100%;
        .illustration-login-style {
            position: absolute;
            top: 215px;
            left: 241px;
            display: flex;
            flex-direction: row;
            border-radius: 2px;
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
            .illustration {
                width: 878px;
                height: 650px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .login-new-style1 {
                position: static;
                width: 560px;
                height: 650px;
                margin-top: 0;
                padding: 74px 66px 0;
                background-color: #fff;
            }
        }
    }
    // .login-style7 {
    //     margin-right: 80px;
    //     &::abroad {
    //         margin-right: 0;
    //     }
    // }

    .otp-binding-tips{
        .text-fade-in{
            display: inline;
        }
    }
}
.login-second-validate-warn-way-message,
.login-second-validate-success-way-message,
.login-second-validate-error-way-message {
    .poppy-message-custom-content {
        display: flex;
    }
}

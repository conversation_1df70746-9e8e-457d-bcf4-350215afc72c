import {Form, Input, message, Modal} from "@streamax/poppy";
import {tt} from "@/utils/i18n";
import "./index.less";
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';
import InfoBack from "@/components/InfoBack";
import {useEffect, useState, useRef} from "react";
import {generateOTPkey, sendEmailToUser} from "@/service/login";
import {BIZ_TYPE} from "@/utils/constant";
import i18n from "i18next";

interface IOTPConfirmModalProps {
    visible: boolean;
    fullEmail: string;
    userInfo: {
        userId: string;
        account: string;
        mailNumber: string;
    },
    lang: string,
    onClose: () => void;
}

export default (props: IOTPConfirmModalProps)=>{

    const { visible, onClose, userInfo, fullEmail, lang } = props;

    const [modalForm] = Form.useForm();
    const [count,setCount] = useState(60);
    const [isCountingDown, setIsCountingDown] = useState(false);
    const [loading,setLoading] = useState(false);
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    const onModalConfirm = async ()=>{
        setLoading(true);
        try {
            const OTPKey = await generateOTPkey({
                    userId: userInfo.userId
                });
            const url = `${window.location.origin}/account/otp/bind?notlogin=true&OTPKey=${OTPKey}?lang=${lang}`;
            await sendEmailToUser(
                {
                    bizType: BIZ_TYPE.OTP_VERIFY,
                    mailNumber: fullEmail,
                    account: userInfo?.account,
                    otpBindUrl: `<a clicktracking=off href='${url}'>${url}</a>`,
                },
                {
                    _langtype: lang,
                }
            );
            setLoading(false);
            setIsCountingDown(true);
            setCount(60);
        // 开始倒计时
        timerRef.current = setInterval(() => {
            setCount(prevCount => {
                if (prevCount <= 1) {
                    setIsCountingDown(false);
                    if (timerRef.current) {
                        clearInterval(timerRef.current);
                        timerRef.current = null;
                    }
                    return 60;
                }
                return prevCount - 1;
            });
        }, 1000);
        }catch (msg){
            message.error(msg);
            setLoading(false);
        }
    };

    // 清理定时器
    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, []);

    // 当模态框关闭时重置状态
    useEffect(() => {
        if (!visible) {
            setIsCountingDown(false);
            setCount(60);
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        }
    }, [visible]);

    useEffect(() => {
        if (userInfo?.mailNumber){
            modalForm.setFieldsValue({
                email: userInfo?.mailNumber
            });
        }
    }, [userInfo?.mailNumber, visible]);

    return  <Modal
        wrapClassName={'OTP-confirm-modal'}
        visible={visible}
        title={tt('@base:@message__second_validate.otp.validate.modal.title')}
        okText={isCountingDown ? `${count}S` : i18n.t('@base:@action__login.active.modal.ok', '确定')}
        cancelText={tt('@base:@action__login.active.modal.cancel', '取消')}
        width={520}
        onCancel={onClose}
        onOk={onModalConfirm}
        okButtonProps={{ disabled: isCountingDown, loading: loading }}
    >
        <InfoBack title={tt('@base:@message__second_validate.otp.validate.title')}/>
        <Form form={modalForm} className={'otp-confirm-form'}>
            <AFormItem
                name="email"
                noStyle={true}
            >
                <Input disabled={true}/>
            </AFormItem>
        </Form>
    </Modal>;
};

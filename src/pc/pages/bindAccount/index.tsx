// @ts-nocheck
import { useState, useEffect, useRef, useMemo } from 'react';
import { history } from 'umi';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { Button, Form, Input, message, Modal } from '@streamax/poppy';
import classNames from 'classnames';
import {
    encryptors,
    getScale,
    getRuntimeLocales,
    queryLoginConfig,
    getOffsetInfo,
    voucherToToken,
} from '@/utils/common';
import { LOGINSTYLE1, LOGIN_STYLE7 } from '../../../utils/constant';
// @ts-ignore
import md5 from 'js-md5';
// import LoginBackImg from '@/component/LoginBackImg';
import usePasswordConfig from '@/hooks/usePasswordConfig';
import { IconTipsFill } from '@streamax/poppy-icons';
import AnimationBg from '../../component/AnimationBg';
import './index.less';
import { useAsyncEffect } from '@streamax/hooks';
import LoginFormBg, { isRightLoginStyle } from '@/pc/component/LoginFormBg';
import BlurImg from '@/pc/component/BlurImg';
import { StarryAbroadFormItem as AFormItem } from '@/pc/themeStyle/StarryAbroadComponents';
import qs from 'querystring';

const defaultLocales = require('../../../app.locale.json');
// RSA 2048 加密
const encryptor = encryptors();

// todo 提取登录和修改密码的公共方法
export default () => {
    const [scale, setScale] = useState(getScale());

    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);

    const [state, setState] = useState(false);

    const [config, setConfig] = useState<any>(null);

    const [illustrationUrl, setIllustrationUrl] = useState<string>(''); // 当前使用的插图url
    const [illustrationHash, setIllustrationHash] = useState<string>('');

    const [illustrationStyle, setIllustrationStyle] = useState<boolean>(false); // 是否使用插图模式

    const [errInfo, setErrInfo] = useState();

    const [bgImageUrl, setBgImageUrl] = useState<string>('');
    const [bgImageHash, setBgImageHash] = useState<string>('');
    const [backGroundLoaded, setBackGroundLoaded] = useState<boolean>(false);
    const query = history.location.query || {};

    useEffect(() => {
        window.addEventListener('resize', () => {
            setScale(getScale());
        });
    }, []);

    // 获取国际化词条
    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType) return;
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        setState(!state);
        // eslint-disable-next-line no-plusplus
    };
    // 获取登录策略
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        try {
            dataConfig = await queryLoginConfig();
            setConfig(dataConfig);
            if (dataConfig?.curUseImageId) {
                const hash = (dataConfig.images || []).find(
                    (item) => item.imageId == dataConfig?.curUseImageId,
                )?.hash;
                setBgImageHash(hash);
                setBgImageUrl(dataConfig?.curUseImageUrl);
            }
            if (dataConfig?.curUseIllustrationId) {
                const hash = (dataConfig.illustration || []).find(
                    (item) => item.imageId == dataConfig?.curUseIllustrationId,
                )?.hash;
                setIllustrationHash(hash);
                setIllustrationUrl(dataConfig?.curUseIllustrationUrl);
            }
            setIllustrationStyle(dataConfig?.loginIllustration || false);
            setBackGroundLoaded(true);
        } catch (e) {
            setConfig({});
            setBackGroundLoaded(true);
        }
        return dataConfig;
    };

    // 计算登录框的左侧的位置只能左侧移动定位，右侧定位不生效
    const calculateLeftLocationDebounce = useMemo(() => {
        // 判断是否右侧布局，不是就返回0
        if (!isRightLoginStyle(config?.loginPageStyle)) return 0;
        const { width } = getOffsetInfo(document.body);
        const leftMax = width - 800 * scale > 0 ? width - 800 * scale : 0;
        return leftMax;
    }, [scale, config?.loginPageStyle]);

    useAsyncEffect(async () => {
        const lng =
            (query.lang as string) ||
            window.localStorage.getItem('LOGIN_LANG') ||
            'en_US';
        const loginConfig = await getLoginConfig();
        await i18nInit(lng as string, loginConfig?.tenantId);
        document.title = i18n.t('@base:@action__bind_account.title');
    }, []);

    // 绑定账号
    const handleBindingAccount = async () => {
        setLoading(true);
        form.validateFields()
            .then((values: any) => {
                const { password, account } = values;
                const lang =
                    (query.lang as string) ||
                    window.localStorage.getItem('LOGIN_LANG') ||
                    'en_US';
                const loginParams = {};
                // 去除用户名的前后空格
                loginParams.account = account ? account.trim() : undefined;
                loginParams.password = password
                    ? encryptor.encrypt(md5(password))
                    : '';
                loginParams.source = (query.third_type as string) || '';
                loginParams.thirdUuid = (query.third_uuid as string) || '';
                loginParams.privacyPolicyVersion =
                    (query.private_policy_version as string) || undefined;
                loginParams.userAgreementVersion =
                    (query.user_policy_version as string) || undefined;
                request
                    .post(
                        '/base-server-service/api/v1/third/user/bind',
                        loginParams,
                        {
                            noDataInterceptor: true,
                        },
                    )
                    .then(async (rs: any) => {
                        const { data } = rs;
                        let resData;
                        const query = history.location.query || {};
                        const redirectUrl = query.redirect_url as string;
                        if (data.code === 200) {
                            resData = data.data;
                            const { voucher } = resData;
                            window.localStorage.setItem(
                                'AUTH_VOUCHER',
                                voucher,
                            );
                            window.localStorage.setItem('LANG', lang);
                            if (redirectUrl) {
                                // 解决安全问题，如果当前重定向的hostname与登录的hostname不同，则默认跳转到首页
                                // if (urlInfo.hostname !== window.location.hostname) {
                                //     window.location.replace('/');
                                //     return;
                                // }
                                const urlInfo = new URL(redirectUrl);
                                const url = getRedirectUrl(urlInfo.hostname);
                                if (url === '/') {
                                    window.location.replace('/');
                                    return;
                                }
                                const urlQuery = qs.parse(
                                    urlInfo.search.replace('?', ''),
                                );

                                urlQuery.auth_voucher = voucher;
                                urlQuery.lang = lang;
                                window.location.replace(
                                    `${urlInfo.origin}${
                                        urlInfo.pathname
                                    }?${qs.stringify(urlQuery)}${urlInfo.hash}`,
                                );
                                return;
                            } else {
                                // 没有redirect_url，则拼接voucher到url
                                window.location.replace(
                                    `${window.location.origin}?auth_voucher=${voucher}`,
                                );
                            }
                        } else if (data.code === *********) {
                            const {
                                voucher,
                                emailNumber,
                                phoneNumber,
                                areaCode,
                                account,
                                userId,
                                verificationMode,
                                defaultMode,
                                tenantId,
                            } = data.data;
                            // 需要进行二次验证
                            sessionStorage.setItem(
                                'second-validate-voucher',
                                voucher,
                            );
                            const params = {
                                redirect_url: redirectUrl,
                                mailNumber: emailNumber,
                                phoneNumber,
                                areaCode,
                                account,
                                userId,
                                verificationMode,
                                defaultMode: defaultMode + '',
                                tenantId,
                            };
                            history.push({
                                pathname: '/login/pc/secondValidate',
                                query: params,
                            });
                        } else if (data.code === 3118004) {
                            // 状态吗  3118004  密码过期或者第一次登陆需要修改密码
                            // 第一次登录修改密码或者密码过期 需要登录获取token
                            resData = data.data;
                            // 信令转为token 出错可以被捕获
                            const {
                                headers: { _token },
                            } = await voucherToToken({
                                voucher: resData.voucher,
                            });
                            window.localStorage.setItem(
                                'UPDATE_PASSWORD_TOKEN',
                                _token as string,
                            );
                            const loginData = {
                                account: resData.account,
                                userId: resData.userId,
                                redirect_url: redirectUrl,
                            };
                            Modal.info({
                                centered: true,
                                title: i18n.t(
                                    '@base:@message__update_password.messagebox.title',
                                    '',
                                ),
                                content: i18n.t(data.langKey),
                                okText: i18n.t(
                                    '@base:@action__update_password.messagebox.confirm',
                                    '',
                                ),
                                onOk: () => {
                                    history.push({
                                        pathname: '/login/pc/updatePassword',
                                        query: loginData,
                                    });
                                },
                                cancelText: false,
                            });
                        } else if ([1448, 1444].includes(data.code)) {
                            setErrInfo(
                                tt(data.langKey, '', {
                                    count: (data['errorVar'] || [])[0],
                                }),
                            );
                        } else {
                            history.push({
                                pathname: '/login/pc/login',
                                query: {
                                    login_failed_code: data.code || '',
                                    langKey: data.langKey || '',
                                    message: data.langKey || '',
                                    lang:
                                        window.localStorage.getItem(
                                            'LOGIN_LANG',
                                        ) || '',
                                },
                            });
                        }
                    });
            })
            .finally(() => {
                setLoading(false);
            });
    };

    if (
        config === null ||
        window.langueCache[
            (query.lang as string) ||
                window.localStorage.getItem('LOGIN_LANG') ||
                'en_US'
        ] === 'loading'
    )
        return null;

    const validatorAccount = (rule: any, value: any) => {
        if (value && !/^(?!.*(&__|\{\s*})).*$/g.test(value)) {
            return Promise.reject(
                i18n.t(
                    '@base:@message__login.username.chat.verification.message',
                    '',
                ),
            );
        }
        return Promise.resolve();
    };

    const renderContent = () => {
        return (
            <div
                className={classNames('bind-account-content-form', {
                    'login-style7': config?.loginPageStyle === LOGIN_STYLE7,
                })}
            >
                <div className="bind-account-form-title">
                    {tt('@base:@action__bind_account.title')}
                </div>
                <Form
                    form={form}
                    autoComplete="off"
                    onKeyPress={(e) => {
                        if (e.nativeEvent && e.nativeEvent.code === 'Enter') {
                            handleBindingAccount();
                        }
                    }}
                >
                    <AFormItem
                        name="account"
                        rules={[
                            {
                                required: true,
                                message: tt(
                                    '@base:@message__login.required.message.username',
                                ),
                            },
                            {
                                validator: validatorAccount,
                            },
                        ]}
                    >
                        <Input
                            autoComplete="off"
                            placeholder={i18n.t(
                                '@base:@message__login.placeholder.username',
                                '',
                            )}
                            size="large"
                        />
                    </AFormItem>

                    <AFormItem
                        name="password"
                        rules={[
                            {
                                required: true,
                                message: tt(
                                    '@base:@message__login.required.message.password',
                                ),
                            },
                        ]}
                    >
                        <Input
                            autoComplete="off"
                            type="password"
                            placeholder={i18n.t(
                                '@base:@message__login.placeholder.password',
                                '',
                            )}
                            size="large"
                        />
                    </AFormItem>
                </Form>
                <Button
                    loading={loading}
                    className="bind-btn"
                    type="primary"
                    onClick={handleBindingAccount}
                >
                    {tt('@base:@action__bind_account.confirm')}
                </Button>
                <div className="error-info">{errInfo}</div>
            </div>
        );
    };

    return (
        <div id="bind-account-container" className="bind-account">
            {backGroundLoaded ? (
                <div
                    className="bind-account-video-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                    }}
                >
                    <AnimationBg
                        bgImageUrl={bgImageUrl}
                        bgImageHash={bgImageHash}
                        loginPageStyle={config?.loginPageStyle}
                    />
                </div>
            ) : null}

            {backGroundLoaded ? (
                <div
                    className="bind-account-bg"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {!illustrationStyle ? (
                        <LoginFormBg loginPageStyle={config?.loginPageStyle} />
                    ) : null}
                </div>
            ) : null}
            {/* {backGroundLoaded ? (
                <>
                    <AnimationBg bgImageUrl={bgImageUrl} />
                    <LoginFormBg />
                </>
            ) : null} */}

            {/* <LoginBackImg bgImageUrl={bgImageUrl}/> */}

            {backGroundLoaded &&
            (illustrationStyle ? illustrationUrl : true) ? (
                <div
                    className="bind-account-wrapper"
                    style={{
                        width: 1920,
                        height: 1080,
                        transformOrigin: '0 0',
                        transform: `scale(${scale})`,
                        position: 'absolute',
                        top: 0,
                        left: calculateLeftLocationDebounce,
                    }}
                >
                    {illustrationStyle &&
                    config.loginPageStyle === LOGINSTYLE1 ? (
                        <div className="illustration-login-style">
                            <div className="illustration">
                                <BlurImg
                                    img={{
                                        url: illustrationUrl,
                                        hash: illustrationHash,
                                    }}
                                />
                            </div>
                            <div className="bind-account-content login-new-style1">
                                {renderContent()}
                            </div>
                        </div>
                    ) : (
                        <div className="bind-account-content">
                            {renderContent()}
                        </div>
                    )}
                </div>
            ) : null}
        </div>
    );
};

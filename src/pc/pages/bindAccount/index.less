@import url('../../style/common');
#root {
    height: 100%;
}

::-webkit-scrollbar {
    display: none !important;
}
@keyframes show-item {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
@keyframes hide-item {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 0;
    }
}
.text-fade-in {
    display: inline-block;
    animation: show-item 0.5s ease-in;
}
.bind-account {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: auto;
    // background-image: url(~@/assets/img_bg.png);
    background-repeat: no-repeat;
    // background-position: top left;
    background-size: 100% 100%;
    &-logo {
        width: 240px;
        height: 60px;
        margin: 64px 0 0 72px;
    }
    &-wrapper {
        width: 100%;
        height: 100%;
    }
    &-content {
        z-index: 10;
        display: flex;
        justify-content: center;
        width: 800px;
        color: #333;

        &-title {
            width: 452px;
            height: 101px;
        }
        &-form {
            position: relative;
            box-sizing: border-box;
            width: 440px;
            margin-top: 250px;
            &::abroad {
                margin-top: 225px;
            }
            // height: 550px;
            .bind-account-form-title {
                width: 100%;
                margin-bottom: 40px;
                color: rgba(0, 0, 0, 0.85);
                font-weight: 600;
                font-size: 28px;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
                text-align: center;
                .login-page-title();
            }
            .error-info {
                height: 16px;
                margin-top: 12px;
                color: red;
                line-height: 16px;
                text-align: center;
            }
            .bind-btn {
                width: 100%;
                height: 48px;
                color: #fff;
                font-weight: 700;
                font-size: 16px;
                font-family: SourceHanSansCN-Bold, sans-serif;
                letter-spacing: 0;
                text-align: center;
                border: none;
                border-radius: 2px;
                &::abroad {
                    height: 56px;
                }
            }
        }
        .login-style7 {
            margin-right: 80px;
            &::abroad {
                margin-top: 260px;
                margin-right: 0;
            }
        }
    }
    .illustration-login-style {
        position: absolute;
        top: 215px;
        left: 241px;
        display: flex;
        flex-direction: row;
        border-radius: 2px;
        box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
        .illustration {
            width: 878px;
            height: 650px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .login-new-style1 {
            width: 560px;
            height: 650px;
            margin-top: 0;
            padding: 74px 66px 0;
            background-color: #fff;
            .bind-account-content-form {
                margin-top: 0;
            }
        }
    }

    &-background-img {
        flex: 1;
        width: 100%;
        background: #ccc;
        background-repeat: no-repeat;
        background-size: cover;
        user-select: none;
        img {
            width: 100%;
            min-height: 100%;
        }
    }
}

.bind-account-bg {
    width: 100%;
    height: 100%;
}

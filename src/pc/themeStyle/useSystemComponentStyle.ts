/**
 * @description 获取当前设置的组件风格相关的Hook
 */
import { useState, useEffect } from 'react';

export const STYLE_ABROAD = 'abroad';
export const STYLE_DEFAULT = 'default';

export const THEME_STYLE_LIGHT = 'light';
export const THEME_STYLE_DARK = 'dark';
export const THEME_STYLE_MIX = 'mix';

let callbacks: any[] = [];
let observer: MutationObserver | null;
let referenceCount = 0;

const createObserver = (attrs: string[]) => {
    return new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            const attrName = mutation.attributeName as string;
            if (mutation.type === 'attributes' && attrs.includes(attrName)) {
                callbacks.forEach((callback) => {
                    callback(
                        attrName,
                        (mutation.target as HTMLBodyElement).getAttribute(
                            attrName,
                        ),
                    );
                });
            }
        });
    });
};

const componentStyleAttr = 'data-component-style';
const themeStyleAttr = 'data-starry-theme-style';

const useSystemComponentStyle = () => {
    const [componentStyle, setComponentStyle] = useState(
        document.body.getAttribute(componentStyleAttr),
    );
    const [currentThemeStyle, setCurrentThemeStyle] = useState(
        document.body.getAttribute(themeStyleAttr),
    );

    useEffect(() => {
        const handleStyleChange = (attr: string, val: string) => {
            if (attr === componentStyleAttr) {
                setComponentStyle(val);
            } else if (attr === themeStyleAttr) {
                setCurrentThemeStyle(val);
            }
        };

        if (!observer) {
            observer = createObserver([componentStyleAttr, themeStyleAttr]);
            observer.observe(document.body, {
                attributes: true,
                attributeFilter: [componentStyleAttr, themeStyleAttr],
            });
        }

        callbacks.push(handleStyleChange);
        referenceCount++;
        return () => {
            callbacks = callbacks.filter((cb) => cb !== handleStyleChange);
            referenceCount--;
            if (referenceCount <= 0) {
                observer?.disconnect();
                observer = null;
            }
        };
    }, []);

    return {
        componentStyle,
        themeStyle: currentThemeStyle,
        isAbroadStyle: componentStyle === STYLE_ABROAD,
        isDark: currentThemeStyle === THEME_STYLE_DARK,
        isLight: currentThemeStyle === THEME_STYLE_LIGHT,
        isMix: currentThemeStyle === THEME_STYLE_MIX,
        STYLE_ABROAD,
        STYLE_DEFAULT,
    };
};

export default useSystemComponentStyle;

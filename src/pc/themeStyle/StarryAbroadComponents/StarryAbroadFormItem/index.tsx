import { FormItemProps } from '@streamax/poppy/lib/form';
import { Form } from '@streamax/poppy';
import useSystemComponentStyle from '../../useSystemComponentStyle';
import { cloneElement, isValidElement } from 'react';
import { SizeType } from '@streamax/poppy/lib/config-provider/SizeContext';

// 默认展示海外大组件
export const DEFAULT_ABROAD_SIZE: SizeType = 'large';

export interface StarryAbroadFormItemProps extends FormItemProps {
    abroadSize?: SizeType;
    children?: any;
}

export default (props: StarryAbroadFormItemProps) => {
    const { children, abroadSize, style, ...rest } = props;
    const { isAbroadStyle } = useSystemComponentStyle();
    const size: SizeType = isAbroadStyle
        ? abroadSize || DEFAULT_ABROAD_SIZE
        : undefined;
    const calcFormItemWidth = isAbroadStyle ? '100%' : style?.width;
    return (
        <Form.Item
            {...rest}
            style={{ ...(style || {}), width: calcFormItemWidth }}
        >
            {children &&
                cloneElement(children, {
                    size,
                    ...(children?.props || {}),
                })}
        </Form.Item>
    );
};

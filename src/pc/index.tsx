/**

 *<AUTHOR>

 *@date 2024/11/14 10:59:56

 *@description: add a description

 **/
import { isMobile } from '@/utils/common';
import { history } from 'umi';
import { ConfigProvider } from '@streamax/poppy';
import './themeStyle/index';

export default (props: { children: React.ReactNode }) => {
    const deviceType = isMobile();
    if (deviceType) {
        const queryParams = history.location.search;
        history.replace({
            pathname: '/login/mobile/login',
            search: queryParams,
        }); // 保留查询参数
    }
    return (
        <ConfigProvider
            getPrefixCls={(className, customPrefixCls = 'poppy') => {
                return `${customPrefixCls}-${className}`;
            }}
        >
            {props.children}
        </ConfigProvider>
    );
};

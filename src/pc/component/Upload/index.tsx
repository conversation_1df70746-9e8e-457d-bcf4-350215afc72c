/*
 * @Author: yxfan
 * @Date: 2024-06-18 15:58:48
 * @LastEditTime: 2024-09-23 11:25:02
 * @FilePath: /base-login-web/src/pages/login/components/PointConfig/Upload.tsx
 * @Description:
 */
// @ts-nocheck
import {
    useState,
    useEffect,
    useRef,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { Upload, Button, message } from '@streamax/poppy';
import i18n from 'i18next';
import { UploadOutlined } from '@ant-design/icons';
import { tt } from '@/utils/i18n';
import './index.less';

const baseUrl = window.APP_CONFIG['gateway.public.url'];

function findKeyByValue(obj, value) {
    // 遍历对象的键值对
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            if (obj[key].fileId == value) {
                return key; // 找到匹配的值时，返回对应的键
            }
        }
    }
    return null; // 如果没有找到匹配的值，返回 null
}

export type UploadFileStatus =
    | 'error'
    | 'success'
    | 'done'
    | 'uploading'
    | 'removed';
export type UploadRef = {
    hasUploading: () => boolean;
    dealFileListErrorTips: (
        data: { fileId: string; errprTips: string }[],
    ) => void;
};

export type FileInfo = {
    fileId: string;
    name: string;
};

export interface UploadFile<T = any> {
    uid: string;
    name: string;
    status?: UploadFileStatus;
    response?: T;
    linkProps?: any;
    originFileObj?: File;
    percent?: number;
    error?: any;
    thumbUrl?: string;
    url?: string;
    type?: string;
    size?: number;
    lastModified?: number;
    lastModifiedDate?: Date;
}
type FileList = {
    uid: string;
    name: string;
    status: UploadFileStatus;
    response?: string;
}[];

interface Props {
    dataProps?: any;
    uploadProps?: any;
    onChange?: (info: string[]) => void;
    value?: string[];
    accept?: string;
}
export default forwardRef((props: Props, ref) => {
    const { dataProps = {}, uploadProps = {}, value, onChange, accept } = props;
    // 零时的文件缓存，用于记录后端返回的文件id 和 文件name用于上传
    const fileUidMapCacheId = useRef<Record<string, FileInfo>>({});
    // 是否有正在上传的文件
    const isUploading = useRef(false);
    const [fileList, setFileList] = useState<FileList>([]);

    useImperativeHandle(ref, () => ({
        hasUploading: () => isUploading.current,
        dealFileListErrorTips,
        setFileList,
    }));

    const finalUploadProps = {
        name: 'file',
        action: `${baseUrl}/base-server-service/api/v1/license/point/file/upload`,
        beforeUpload,
        onChange: uploadChange,
        onRemove,
        data: {
            ...dataProps,
        },
        ...uploadProps,
    };

    /**
     * @description 设置fileList的错误提示信息
     * @param {{ fileId: string, errprTips: string }[]} data
     */
    function dealFileListErrorTips(
        data: { fileId: string; errprTips: string }[],
    ) {
        data.forEach(({ fileId, errprTips }) => {
            const fileUid = findKeyByValue(fileUidMapCacheId.current, fileId);
            if (fileUid) {
                const targetFile = fileList.find(
                    (item) => item.uid === fileUid,
                );
                if (targetFile) {
                    targetFile.status = 'error';
                    targetFile.response = errprTips;
                }
            }
        });
        setFileList([...fileList]);
    }

    function beforeUpload(file: UploadFile) {
        const isText = file.name.endsWith(`${accept}`);
        if (!isText) {
            message.error(
                i18n.t('@base:@message__upload.file.type.error', {
                    accept: accept,
                }),
            );
            return Upload.LIST_IGNORE;
        }

        const isSize = file.size / 1024 / 1024 <= 200;
        if (!isSize) {
            message.error(
                i18n.t('@base:@message__upload.file.size.error', {
                    size: 200,
                }),
            );
            return Upload.LIST_IGNORE;
        }
    }

    function hasUploadingTask(fileList: UploadFile[]) {
        for (let item of fileList) {
            if (item.status === 'uploading') {
                isUploading.current = true;
                return;
            }
        }
        isUploading.current = false;
    }

    function uploadChange(info) {
        const { fileList } = info;
        hasUploadingTask(fileList);

        if (info.file.status === 'done') {
            const { response } = info.file;
            if (response.code === 200) {
                updateTmp('update', info.file.uid, {
                    fileId: response.data + '',
                    fileName: info.file.name,
                });
            } else {
                info.file.status = 'error';
            }
        }

        if (info.file.status === 'error') {
            message.error(
                i18n.t('@base:@message__upload.file.upload.error.tips'),
            );
            info.file.response = i18n.t('@base:@return__120020106');
            const newFileList = fileList.filter(
                (item: UploadFile) => item.uid != info.file.uid,
            );
            setFileList(newFileList);
        } else {
            setFileList(fileList);
        }
    }

    function updateTmp(
        type: 'update' | 'delete',
        uid: string,
        fileInfos?: FileInfo,
    ) {
        if (type === 'delete') {
            if (fileUidMapCacheId.current[uid]) {
                delete fileUidMapCacheId.current[uid];
            }
        } else if (fileInfos?.fileId) {
            // 根据antd官网规范 当且仅当maxCount为1时自动替换
            if (uploadProps?.maxCount == 1) {
                if (
                    Object.values(fileUidMapCacheId.current)?.length >=
                    uploadProps.maxCount
                ) {
                    const fileArray = Object.entries(fileUidMapCacheId.current);
                    const deleteFile = fileArray.shift();
                    fileUidMapCacheId.current = Object.fromEntries(fileArray);
                }
            }
            fileUidMapCacheId.current[uid] = fileInfos;
        }
        onChange?.(Object.values(fileUidMapCacheId.current));
    }

    function onRemove(file: UploadFile) {
        updateTmp('delete', file.uid);
    }

    return (
        <div className="active-point-upload">
            <Upload accept={accept} {...finalUploadProps} fileList={fileList}>
                <Button icon={<UploadOutlined />}>
                    {tt('@base:@action__upload.file.button', '上传文件')}
                </Button>
                <div
                    className="tips"
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    }}
                >
                    {tt(
                        '@base:@message__upload.file.type.tips',
                        '仅支持扩展名:',
                    )}
                    {`${accept}`}
                </div>
            </Upload>
        </div>
    );
});

/*
 * @LastEditTime: 2022-11-03 17:55:12
 */
import './index.less';
export type LoginBackImgProps = {
    bgImageUrl: string;
};

export default (props: LoginBackImgProps) => {
    return (
        <div
            className="login-back-img-container"
            style={{
                backgroundImage: `url(${
                    props.bgImageUrl
                        ? props.bgImageUrl
                        : require('./../../assets/gif_default_bg.gif')
                })`,
            }}
        >
            {/* <img className='login-back-image' src={props.bgImageUrl
                    ? props.bgImageUrl
                    : require('./../../../assets/git_default_bg.gif')} alt="" /> */}
            <div className="container"></div>
            <svg width={925} height={1080} id="svg">
                <defs>
                    <linearGradient id="lgg" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop
                            offset="0%"
                            style={{
                                stopColor: 'rgb(255,255,255)',
                                stopOpacity: '0.8',
                            }}
                        />
                        <stop
                            offset="100%"
                            style={{
                                stopColor: 'rgb(255,255,255)',
                                stopOpacity: '0.2',
                            }}
                        />
                    </linearGradient>
                </defs>
                <path
                    d="M800 -2 C1050 250 800 400 800 540 C800 680 1050 830 800 1080"
                    // style={{ fill: 'url(#lgg)' }}
                    fill="none"
                    style={{ stroke: '#eee', strokeWidth: 4 }}
                ></path>
            </svg>
        </div>
    );
};

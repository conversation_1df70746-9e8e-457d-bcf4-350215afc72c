import {
    BG_CONTAINER_HEIGHT,
    BG_WITH_LOGO_CONTAINER_HEIGHT,
    LOGINSTYLE1,
    LOGINSTYLE2,
    LOGINSTYLE3,
    LOGINSTYLE4,
    LOGIN_STYLE5,
    LOGIN_STYLE6,
    LOGIN_STYLE7,
} from '@/utils/constant';
import cn from 'classnames';
import './index.less';
import useSystemComponentStyle from '@/pc/themeStyle/useSystemComponentStyle';
import { loginTitleStyleEnum } from '@/type';
import React from 'react';

interface LoginBaseProps {
    pathBlock?: React.ReactElement;
    pathLine?: React.ReactElement;
    last?: boolean;
    innerContainerStyle?: React.CSSProperties;
}

/**
 * 是否是右则样式
 */
export const isRightLoginStyle = (loginPageStyle: number) =>
    [LOGIN_STYLE5, LOGIN_STYLE6, LOGIN_STYLE7].includes(loginPageStyle);

/**
 * 通过海外滑块何文字和logo展示控制高度
 * @param params
 * @returns
 */
export const calcContainerHight = (params: {
    originalValue: number[] | number;
    isAbroadStyle: boolean;
    logoTitleHeight: number;
    sliderVerifyCodeVisible: boolean;
}) => {
    const {
        originalValue,
        isAbroadStyle,
        logoTitleHeight,
        sliderVerifyCodeVisible,
    } = params || {};
    let result = Array.isArray(originalValue) ? originalValue : [originalValue];
    // 滑块打开不用设置高度
    if (sliderVerifyCodeVisible) return result;
    if (isAbroadStyle) {
        // 海外风格加24px
        result = result.map((item) => item + 24);
    }
    if (logoTitleHeight) {
        result = result.map((item) => item + logoTitleHeight);
    }
    return result;
};

const LoginBase = (props: LoginBaseProps) => {
    const defs = (
        <defs>
            <linearGradient id="lgg" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop
                    offset="0%"
                    style={{
                        stopColor: 'rgb(255,255,255)',
                        stopOpacity: '0.8',
                    }}
                />
                <stop
                    offset="100%"
                    style={{
                        stopColor: 'rgb(255,255,255)',
                        stopOpacity: '0.2',
                    }}
                />
            </linearGradient>
        </defs>
    );

    return (
        <>
            <div
                style={props?.innerContainerStyle}
                className={cn({
                    container: true,
                    container4: props.last,
                })}
            ></div>
            <svg width={925} height={1080} id="svg">
                {defs}
                {props?.pathBlock}
            </svg>
            <svg height="0" width="0">
                <defs>
                    <clipPath id="svgPath">{props.pathLine}</clipPath>
                </defs>
            </svg>
        </>
    );
};

const getContainerInitHeight = ({
    sliderVerifyCodeVisible,
    loginTitleStyle,
}: {
    loginTitleStyle: loginTitleStyleEnum;
    sliderVerifyCodeVisible: boolean;
}) => {
    if (sliderVerifyCodeVisible) return BG_CONTAINER_HEIGHT;
    return loginTitleStyle
        ? BG_WITH_LOGO_CONTAINER_HEIGHT
        : BG_CONTAINER_HEIGHT;
};

const LoginFormBg = ({
    loginPageStyle,
    loginTitleStyle,
    sliderVerifyCodeVisible,
    logoTitleHeight,
}: {
    loginPageStyle: number;
    loginTitleStyle: loginTitleStyleEnum;
    sliderVerifyCodeVisible: boolean;
    logoTitleHeight: number;
}) => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const baseContainerHeight = getContainerInitHeight({
        loginTitleStyle,
        sliderVerifyCodeVisible,
    });

    const LoginStyle1 = () => {
        const PathBlock = (
            <path
                d="M800 -2 C1050 250 800 400 800 540 C800 680 1050 830 800 1080"
                fill="none"
                style={{ stroke: '#eee', strokeWidth: 4 }}
            ></path>
        );
        const PathLine = (
            <path
                id="heart"
                d="M0,-1 L800,0 C1050 250 800 400 800 540 C800 680 1050 830 800 1080 L0,1080"
            />
        );
        return <LoginBase pathBlock={PathBlock} pathLine={PathLine} />;
    };
    const LoginStyle2 = () => {
        const PathBlock = (
            <path
                d="M800 -2 L800,1080"
                fill="none"
                style={{ stroke: '#eee', strokeWidth: 4 }}
            ></path>
        );
        const PathLine = <path id="heart" d="M0,-1 L800,0 L800,1080 L0,1080" />;
        return <LoginBase pathBlock={PathBlock} pathLine={PathLine} />;
    };
    const LoginStyle3 = () => (
        <>
            <div className="login-style3"></div>
        </>
    );

    const getLoginStyle4ConfigPath = () => {
        const isMove =
            loginTitleStyle === loginTitleStyleEnum.MIX &&
            !sliderVerifyCodeVisible;
        const offsetMoveTopDis = 90;
        // 定义高度变量. 混合模式移动的高度差90
        let topY = 200; // 顶部起点Y坐标
        let bottomY = 780; // 底部终点Y坐标
        let cornerControlY = 216; // 顶部圆角的控制点Y坐标
        let bottomControlY = 764; // 底部圆角的控制点Y坐标

        if (isMove) {
            topY = topY - offsetMoveTopDis;
            bottomY = bottomY - offsetMoveTopDis;
            cornerControlY = cornerControlY - offsetMoveTopDis;
            bottomControlY = bottomControlY - offsetMoveTopDis;
        }
        // 容器高度需要动态调整
        if (baseContainerHeight === BG_WITH_LOGO_CONTAINER_HEIGHT) {
            const distance =
                BG_CONTAINER_HEIGHT - BG_WITH_LOGO_CONTAINER_HEIGHT;
            // 需要动态调整高度
            bottomY = bottomY - distance;
            bottomControlY = bottomControlY - distance;
        }
        return {
            topY,
            bottomY,
            cornerControlY,
            bottomControlY,
        };
    };

    const LoginStyle4 = () => {
        const { topY, bottomY, cornerControlY, bottomControlY } =
            getLoginStyle4ConfigPath();
        const [
            newBottomControlY,
            newBottomY,
            pathLineHeight,
            innerContainerHight,
        ] = calcContainerHight({
            originalValue: [
                bottomControlY,
                bottomY,
                baseContainerHeight,
                baseContainerHeight,
            ],
            isAbroadStyle,
            logoTitleHeight,
            sliderVerifyCodeVisible,
        });

        // 使用模板字符串构造路径数据
        const pathData = `
            M136 ${topY}
            L664,${topY}
            Q680,${topY},680,${cornerControlY}
            L680,${newBottomControlY}
            Q680,${newBottomY},664,${newBottomY}
            L136,${newBottomY}
            Q120,${newBottomY},120,${newBottomControlY}
            L120,${cornerControlY}
            Q120,${topY},136,${topY}
            `;
        const PathBlock = (
            <path
                d={pathData.trim()}
                fill="none"
                style={{ stroke: '#eee', strokeWidth: 4, borderRadius: '16px' }}
            ></path>
        );
        const PathLine = (
            <path
                id="heart"
                d={`M0,0 L560,0 L560,${pathLineHeight} L0,${pathLineHeight} L0,0`}
            />
        );
        return (
            <LoginBase
                pathBlock={PathBlock}
                pathLine={PathLine}
                innerContainerStyle={{
                    height: innerContainerHight,
                    marginTop:
                        !sliderVerifyCodeVisible &&
                        loginTitleStyle === loginTitleStyleEnum.MIX
                            ? 110
                            : 200,
                }}
                last
            />
        );
    };

    const LoginStyle5 = () => {
        const PathLine = <path id="heart" d="M0,-1 L800,0 L800,1080 L0,1080" />;
        return <LoginBase pathBlock={undefined} pathLine={PathLine} />;
    };
    const LoginStyle6 = () => <div className="login-style6" />;

    const LoginStyle7 = () => {
        const [baseLoginStyle7Height] = calcContainerHight({
            originalValue: [baseContainerHeight],
            isAbroadStyle,
            logoTitleHeight,
            sliderVerifyCodeVisible,
        });
        return (
            <div
                style={{
                    height: baseLoginStyle7Height,
                    marginTop:
                        !sliderVerifyCodeVisible &&
                        loginTitleStyle === loginTitleStyleEnum.MIX
                            ? 110
                            : 200,
                }}
                className="login-style7"
            >
                <div className="inner-container" />
            </div>
        );
    };

    const getLoginStyle = (type: number) => {
        switch (type) {
            case LOGINSTYLE1:
                return LoginStyle1();
            case LOGINSTYLE2:
                return LoginStyle2();
            case LOGINSTYLE3:
                return LoginStyle3();
            case LOGINSTYLE4:
                return LoginStyle4();
            case LOGIN_STYLE5:
                return LoginStyle5();
            case LOGIN_STYLE6:
                return LoginStyle6();
            case LOGIN_STYLE7:
                return LoginStyle7();
            default:
                return LoginStyle1();
        }
    };

    return (
        <main className="login-form-bg-container">
            {getLoginStyle(loginPageStyle)}
        </main>
    );
};

export default LoginFormBg;

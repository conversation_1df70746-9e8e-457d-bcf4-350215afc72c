.base-container {
    position: absolute;
    top: 0;
    height: 100%;
    background-image: linear-gradient(
        -90deg,
        rgba(255, 255, 255, 0.2) 17%,
        rgba(255, 255, 255, 0.85) 61%
    );
    // filter: blur(20px);
    backdrop-filter: blur(10px);
    clip-path: url(#svgPath);
}
.base-container4 {
    position: absolute;
    top: 0;
    left: 120px;
    width: 560px;
    margin-top: 200px;
    background-image: linear-gradient(
        -90deg,
        rgba(255, 255, 255, 0.2) 17%,
        rgba(255, 255, 255, 0.85) 61%
    );
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.login-form-bg-container {
    position: absolute;
    width: 100%;
    height: 100%;
    .container {
        .base-container;
        left: 0;
        width: 925px;
    }
    .right-container {
        .base-container;
        right: 0;
        width: 800px;
    }
    #svg {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    }
    .login-back-image {
        width: 1920px;
        width: 100%;
        height: 1080px;
        height: 100%;
        font-size: 0;
        user-select: none;
    }
    .video-background {
        object-fit: fill;
    }

    .login-style3 {
        position: absolute;
        top: 0;
        left: 0;
        width: 800px;
        height: 100%;
        background-color: #fff;
    }

    .login-style6 {
        width: 800px;
        height: 100%;
        background-color: #fff;
    }

    .login-style7 {
        width: 800px;
        height: 580px;
        margin-top: 200px;
        .inner-container {
            width: 560px;
            height: 100%;
            margin-right: 160px;
            margin-left: 80px;
            background-color: #fff;
            &::abroad {
                margin-left: 120px;
                border-radius: 8px;
            }
        }
    }
    .container4 {
        .base-container4;
        height: 600px;
    }
    .container4::abroad {
        height: 624px;
    }
}

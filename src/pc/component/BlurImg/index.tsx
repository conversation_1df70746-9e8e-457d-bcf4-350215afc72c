import React, { useState, useRef, useEffect } from 'react';
import './index.less';
import * as thumbHash from 'thumbhash';

interface BlurImgProps {
    loaded: () => void;
    img: {
        // thumbhash 值
        hash: string;
        // 图片地址
        url: string;
    };
}

const BlurImg: React.FC<BlurImgProps> = (props) => {
    const { hash, url } = props.img;
    const { loaded: loadedFn } = props;
    const [loaded, setLoaded] = useState(false);
    const blurImgRef = useRef<HTMLImageElement>(null);
    const imgLoaded = () => {
        setLoaded(true);
        // 便于poster切换视频流畅，待blur动画(1s)完成后触发loadedFn
        setTimeout(() => {
            loadedFn?.();
        }, 1000);
    };

    useEffect(() => {
        if (hash && blurImgRef.current) {
            //@ts-ignore
            blurImgRef.current.src = thumbHash.thumbHashToDataURL(
                hash.split(','),
            );
        }
    }, [hash]);

    return (
        <div className={`hash-blur-img`}>
            <img
                ref={blurImgRef}
                className={`blur-img ${loaded ? 'not-visible' : ''}`}
            />
            <img className="origin-img" onLoad={imgLoaded} src={url} />
        </div>
    );
};

export default BlurImg;

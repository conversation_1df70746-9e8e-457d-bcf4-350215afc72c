/*
 * @LastEditTime: 2023-08-25 13:16:51
 */
import { useState, useRef, useEffect } from 'react';
import BlurImg from '../BlurImg';
import { isRightLoginStyle } from '../LoginFormBg';
import './index.less';

export type LoginBackImgProps = {
    bgImageUrl: string;
    bgImageHash: string;
    loginPageStyle: number;
};

//默认背景图的thumbhash值，可从管理后台上传生成
const default_bg_right_hash =
    '179,247,5,4,128,112,101,134,97,151,136,248,55,119,112,97,8,7,118';
const default_bg_hash =
    '179,247,5,4,128,127,106,137,145,103,120,8,199,135,127,145,248,7,121';
export const white_bg_hash =
    '63,8,2,6,128,135,135,136,135,119,120,143,120,120,119,8,119,120,0,0,0,0,0';

export default ({
    bgImageUrl,
    bgImageHash,
    loginPageStyle,
}: LoginBackImgProps) => {
    const [showImg, setShowImg] = useState(true);
    const [blurImgLoaded, setBlurImgLoaded] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);
    // 有自定义背景视频Url
    const customVideoUrl = window.APP_CONFIG['custom.login.bg.video.url'];

    // 有自定义背景图片Url
    const customImgUrl = window.APP_CONFIG['custom.login.bg.img.url'];

    const [customVideoSrc, setCustomVideoSrc] = useState('');
    const [customImgSrc, setCustomImgSrc] = useState('');

    useEffect(() => {
        if (customVideoUrl && customVideoUrl != '0') {
            setCustomVideoSrc(customVideoUrl);
        }
        if (customImgUrl && customImgUrl != '0') {
            setCustomImgSrc(customImgUrl);
        }
    }, [customVideoUrl, customImgUrl]);

    const getImg = () => {
        if (!customImgUrl || customImgUrl == '0') {
            const img = {
                hash: bgImageHash || white_bg_hash,
                url: bgImageUrl,
            };
            if (!bgImageUrl) {
                if (isRightLoginStyle(loginPageStyle)) {
                    img.hash = default_bg_right_hash;
                    img.url = require('@/assets/default_bg_right.png');
                } else {
                    img.hash = default_bg_hash;
                    img.url = require('@/assets/default_bg.png');
                }
            }
            return img;
        } else {
            return {
                url: customImgSrc,
                hash: bgImageHash || white_bg_hash,
            };
        }
    };

    const play = () => {
        if (blurImgLoaded && videoRef.current) {
            setShowImg(false);
            videoRef.current.play();
        }
    };

    useEffect(() => {
        play();
    }, [blurImgLoaded]);

    const getVideoSrc = () => {
        if (!customVideoUrl || customVideoUrl == '0') {
            return isRightLoginStyle(loginPageStyle)
                ? require('@/assets/right-background-8s-l-new.mp4')
                : require('@/assets/default-background-8s-l-new.mp4');
        } else {
            return customVideoSrc;
        }
    };

    return (
        <div className="login-back-img-container">
            {showImg && (
                <div className="background-img">
                    <BlurImg
                        loaded={() => setBlurImgLoaded(true)}
                        img={getImg()}
                    />
                </div>
            )}
            {!bgImageUrl && (
                <video
                    className="video-background"
                    src={getVideoSrc()}
                    autoPlay
                    ref={videoRef}
                    onCanPlay={play}
                    loop
                    muted
                    width={1920}
                    height={1080}
                ></video>
            )}
        </div>
    );
};

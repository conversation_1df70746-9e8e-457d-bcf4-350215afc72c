import { useEffect, useState } from 'react';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { VERIFY_SPECIAL_CHARACTER_REGEXP } from '@/utils/constant';
import { Rule } from '@streamax/poppy/lib/form';

interface IPwdConfig {
    minDigit: number;
    maxDigit: number;
    includeUpperLowerChar: 0 | 1; // 0：不开启 1： 开启
    includeSpecialChar: 0 | 1;
}

export default (isMobile = false) => {
    const [pwdConfig, setPwdConfig] = useState<IPwdConfig>({
        minDigit: 6,
        maxDigit: 20,
        includeUpperLowerChar: 1,
        includeSpecialChar: 1,
    });

    useEffect(() => {
        getConfig();
    }, []);

    // 获取密码校验规则
    function getConfig() {
        request
            .get('/base-server-service/api/v1/tenant/config/pwd/query', {
                headers: {
                    _token: localStorage.getItem('UPDATE_PASSWORD_TOKEN'),
                },
            })
            .then((res: any) => {
                if (res) {
                    setPwdConfig(res);
                }
            });
    }
    // 校验用户密码
    function validatorPC(rule: any, value: any) {
        return validatorPwd(
            value,
            Promise.reject.bind(Promise),
            Promise.resolve.bind(Promise),
        );
    }
    function validatorMobile(value: any, callback: any) {
        return validatorPwd(value, callback, callback);
    }
    function validatorPwd(
        value: any,
        errorMessageCallback: any,
        successMessageCallback?: any,
    ) {
        const {
            includeUpperLowerChar,
            includeSpecialChar,
            minDigit,
            maxDigit,
        } = pwdConfig;
        if (value.length < (minDigit || 6)) {
            if (value) {
                return errorMessageCallback(
                    i18n.t(
                        '@base:@message__update_password.required.message.password_length',
                        {
                            count: minDigit || 6,
                        },
                    ),
                );
            }
        }
        if (value.length > (maxDigit || 20)) {
            if (value) {
                return errorMessageCallback(
                    i18n.t(
                        '@base:@message__update_password.required.message.password_length.max',
                        {
                            count: maxDigit || 20,
                        },
                    ),
                );
            }
        }

        if (
            includeUpperLowerChar === 1 &&
            (!/[a-z]+/g.test(value) || !/[A-Z]+/g.test(value))
        ) {
            if (value) {
                return errorMessageCallback(
                    i18n.t(
                        '@base:@message__update_password.message.capital_password',
                    ),
                );
            }
        }
        if (
            includeSpecialChar === 1 &&
            !new RegExp(VERIFY_SPECIAL_CHARACTER_REGEXP).test(value)
        ) {
            if (value) {
                return errorMessageCallback(
                    i18n.t(
                        '@base:@message__update_password.message.special_password',
                    ),
                );
            }
        }
        return successMessageCallback && successMessageCallback?.();
    }

    // 编辑的时候密码非必填

    const pwdRules: Rule[] = [
        {
            validator: validatorPC,
        },
    ];
    const pwdRulesMobile: any[] = [
        {
            type: 'custom',
            validator: validatorMobile,
        },
    ];
    return {
        pwdConfig,
        pwdRules: isMobile ? pwdRulesMobile : pwdRules,
    };
};

import i18n from 'i18next';
import { tt } from './i18n';

// 校验密码
export const password = (rule: any, value: any) => {
    if (value && !/^(?=[a-zA-Z\d])./g.test(value.trim())) {
        return Promise.reject(i18n.t('message', '不能以符号开头'));
    }

    if (
        value &&
        // eslint-disable-next-line no-useless-escape
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[()`~!@#$%^&*\-+=_|{}[\]:;'<>,\.?/])[A-Za-z\d()`~!@#$%^&*\-+=_|{}[\]:;'<>,\.?/]{8,20}/g.test(
            value.trim(),
        )
    ) {
        return Promise.reject(
            i18n.t('message', '需要包含数字、符号、大小写字母'),
        );
    }

    return Promise.resolve();
};

export const illegalCharacter = (rule: any, value: string) => {
    if (value && /[\\/*,？?‘’''“”""<>|｜]/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};

// 只能输入数字或字母
export const numberOrLetter = (rule: any, value: string) => {
    if (value && !/^[0-9a-zA-Z]+$/gi.test(value)) {
        return Promise.reject(i18n.t('message', '只能输入字母或数字'));
    }
    return Promise.resolve();
};

// 只能输入英文字母、数字、下划线、邮箱字符（@._）
/**
 *
 * @param rule
 * @param value
 * @returns
 */
export const name = (rule: any, value: string) => {
    if (value && !/^[0-9a-zA-Z_\s@._]+$/g.test(value)) {
        return Promise.reject(
            i18n.t('message', '只能输入英文字母、数字、下划线、邮箱字符'),
        );
    }
    return Promise.resolve();
};

// 确认两次输入密码相同
export const validatorNewPassword = ({ getFieldValue }: any) => ({
    validator(_: any, value: any) {
        if (!value || getFieldValue('newPassword') === value) {
            return Promise.resolve();
        }
        return Promise.reject(
            i18n.t('@base:@message__update_password.message.same_password'),
        );
    },
});
// 验证输入完整的邮箱/电话格式是否正确
export const validateEmailOrPhone = (
    _: any,
    value: string,
    numberType: string,
) => {
    if (numberType === 'phoneNumber') {
        if (!value || !/^[0-9-]+$/.test(value) || /^[-]+$/.test(value)) {
            return Promise.reject(
                tt('@base:@message__reset_password.input.right.phone'),
            );
        } else if (value.length < 6 || value.length > 15) {
            return Promise.reject(
                tt('@base:@message__user_phone.input.length.tips'),
            );
        } else {
            return Promise.resolve();
        }
    } else {
        const emailReg =
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if (!value) {
            // 为空、和从接口获取的邮箱不一致
            return Promise.reject(
                tt('@base:@message__reset_password.input.right.email'),
            );
        } else if (!emailReg.test(value)) {
            return Promise.reject(
                tt('@base:@message__reset_password.email.format.error'),
            );
        } else {
            return Promise.resolve();
        }
    }
};
// 验证是否是邮箱格式
export const getEmailOwnRule = () => {
    return {
        type: 'email',
        message: i18n.t('@base:@message__reset_password.email.format.error'),
    };
};

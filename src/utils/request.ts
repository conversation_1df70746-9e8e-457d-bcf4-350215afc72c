import axios from 'axios';
import i18n from 'i18next';

const request = axios.create({
    baseURL: window.APP_CONFIG['gateway.public.url'],
    // timeout: 1000,
    // headers: {'X-Custom-Header': 'foobar'}
});
request.interceptors.response.use(
    (rs) => {
        const { data, config } = rs;
        if (config.noDataInterceptor) {
            return rs;
        }
        return data.code === 200 || data.code === '200'
            ? data.data
            : Promise.reject(i18n.t(data.langKey));
    },
    (err) => {
        return Promise.reject(err.message || 'Uncaught error');
    },
);
export default request;

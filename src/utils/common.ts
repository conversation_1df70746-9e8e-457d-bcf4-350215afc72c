// @ts-nocheck
import { JSEncrypt } from 'js-encrypt';
import request from '@/utils/request';
import { VERIFY_SPECIAL_CHARACTER_REGEXP } from '@/utils/constant';
import { i18n } from '@/i18n/exports';
import {tt} from "@/utils/i18n";

// RSA 2048 加密
const encryptors = () => {
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(
        decodeURIComponent(window.APP_CONFIG['password.rsa2048.public.key']),
    );
    return encryptor;
};

export function getOffsetInfo(element: HTMLElement) {
    const ret = { top: 0, left: 0, width: 0, height: 0 };
    if (!element) {
        return ret;
    }

    if (!element.getClientRects().length) {
        return ret;
    }
    const rect = element.getBoundingClientRect();

    if (rect.width || rect.height) {
        const doc = element.ownerDocument;
        const docElem = doc.documentElement;
        return {
            top: Math.round(rect.top - docElem.clientTop),
            left: Math.round(rect.left - docElem.clientLeft),
            width: Math.round(rect.width),
            height: Math.round(rect.height),
        };
    }

    return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: 0,
        height: 0,
    };
}
// 界面缩放调整
const getScale = () => {
    const { width, height } = getOffsetInfo(document.body);
    const x = width / 1920;
    const y = height / 1080;
    if (x > y) return x;
    return y;
};

// 拉取词条
const getRuntimeLocales = async (type: string, tenantId?: string) => {
    const langueCache = window.langueCache;
    if (langueCache[type]) {
        return langueCache[type];
    }
    const params = {
        params: {
            langType: type.toUpperCase(),
            // tenantId: '-1',
        },
    };
    if (tenantId) {
        params.headers = {
            _tenantId: tenantId,
        };
    }
    try {
        langueCache[type] = 'loading';
        const data = await request.get(
            '/base-server-service/api/v1/language/front/list',
            params,
        );

        const result = (data || []).reduce((ret: any, item: any) => {
            return {
                ...ret,
                [item.langKey]: !['', null, undefined].includes(
                    item.translationValue,
                )
                    ? item.translationValue
                    : item.zhCnKey,
            };
        }, {});
        langueCache[type] = result;
        return result;
    } catch (e) {
        langueCache[type] = {};
        // delete langueCache[type];
        return {};
    }
};
// 获取密码配置
const queryLoginConfig = async () => {
    const domainName =
        process.env.NODE_ENV === 'development'
            ? window.APP_CONFIG['gateway.public.url']
                  .replace('/gateway', '')
                  .split(':')[1]
                  .replace('//', '')
            : window.location.hostname;
    const data =
        (await request.get('/base-server-service/api/v1/domain/detail', {
            // 加上斜杠，和referer字段保持一致,不是很理解明明可以从header的referer字段取，为啥还要新加一个字段
            params: { domainName: window.encodeURIComponent(`${domainName}`) },
        })) || {};
    return data;
};

export { encryptors, getScale, getRuntimeLocales, queryLoginConfig };

// 用户协议版本号
export const USERAGREEMENTVERSION = 'V1.0';
// 隐私政策版本号
export const PRIVACYPOLICYVERSION = 'V1.0';
// 手机号*替换中间4位
export const getSecritePhone = (phoneNumber: string) => {
    const reg = /(\d{3})(\d{4})(\d{4})/gi;
    return phoneNumber.length ? phoneNumber.replace(reg, '$1****$3') : '';
};
// 邮箱*替换
export const getSecriteEmail = (email: string) => {
    if (!email) {
        return '';
    } else {
        const strs = email.split('@');
        const newStr =
            strs[0].substring(0, strs[0].length - 2) + '****' + '@' + strs[1];
        return newStr;
    }
};
// 校验验证码格式是否正确
export const validateAuthCode = (
    _: any,
    value: string,
    validateWay: string,
) => {
    if (!value) {
        if (validateWay === "OTPCode"){
            return Promise.reject(
                i18n.t('@base:@message__second_validate.otp.validate.empty', ''),
            );
        }
        return Promise.reject(
            i18n.t('@base:@message__login.required.message.auth_code', ''),
        );
    } else {
        if (validateWay === 'OTPCode') {
            return Promise.resolve();
        } else if (validateWay === 'phoneNumber') {
            // 手机 只能是数字
            if (/^[0-9]*$/.test(value)) {
                return Promise.resolve();
            } else {
                return Promise.reject(
                    i18n.t(
                        '@base:@message__login.format.message.auth_code',
                        '',
                    ),
                );
            }
        } else {
            // 邮箱 数字、大小写字母
            if (/^[0-9a-zA-Z]*$/.test(value)) {
                return Promise.resolve();
            } else {
                return Promise.reject(
                    i18n.t(
                        '@base:@message__login.format.message.auth_code',
                        '',
                    ),
                );
            }
        }
    }
};
// 将信令转换为token
export const voucherToToken = async (params: any) => {
    const data = await request({
        method: 'post',
        url: '/base-server-service/api/v1/user/newSecurityToken',
        data: params,
        noDataInterceptor: true,
    });
    return data;
};
interface ValidatePwdParams {
    includeSpecialChar: number;
    includeUpperLowerChar: number;
    maxDigit: number;
    minDigit: number;
}
export const validatePassword = (
    value: string,
    pwdConfig: ValidatePwdParams,
) => {
    const { includeUpperLowerChar, includeSpecialChar, minDigit, maxDigit } =
        pwdConfig;
    if (value.length < (minDigit || 6)) {
        if (value) {
            return Promise.reject(
                i18n.t(
                    '@base:@message__update_password.required.message.password_length',
                    {
                        count: minDigit || 6,
                    },
                ),
            );
        }
    }
    if (value.length > (maxDigit || 20)) {
        if (value) {
            return Promise.reject(
                i18n.t(
                    '@base:@message__update_password.required.message.password_length.max',
                    {
                        count: maxDigit || 20,
                    },
                ),
            );
        }
    }

    if (
        includeUpperLowerChar === 1 &&
        (!/[a-z]+/g.test(value) || !/[A-Z]+/g.test(value))
    ) {
        if (value) {
            return Promise.reject(
                i18n.t(
                    '@base:@message__update_password.message.capital_password',
                    '',
                ),
            );
        }
    }
    if (
        includeSpecialChar === 1 &&
        !new RegExp(VERIFY_SPECIAL_CHARACTER_REGEXP).test(value)
    ) {
        if (value) {
            return Promise.reject(
                i18n.t(
                    '@base:@message__update_password.message.special_password',
                    '',
                ),
            );
        }
    }
    return Promise.resolve();
};
export const getOs = () => {
    const ua = navigator.userAgent,
        isWindowsPhone = /(?:Windows Phone)/.test(ua),
        isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
        isAndroid = /(?:Android)/.test(ua),
        isFireFox = /(?:Firefox)/.test(ua),
        isTablet =
            /(?:iPad|PlayBook)/.test(ua) ||
            (/(?:Macintosh)/.test(ua) && navigator.maxTouchPoints > 0) ||
            (isAndroid && !/(?:Mobile)/.test(ua)) ||
            (isFireFox && /(?:Tablet)/.test(ua)),
        isPhone = /(?:iPhone)/.test(ua) && !isTablet,
        isPc = !isPhone && !isAndroid && !isSymbian;
    window.console.log(ua);
    window.console.log({
        platform: navigator.platform,
        maxTouchPoints: navigator.maxTouchPoints,
    });
    return {
        isAndroid,
        isPhone,
        isTablet,
        isPc,
    };
};

export const getFilesUrlById = async (
    params: string[] | number[] | string = [],
) => {
    try {
        const fileIdList = Array.isArray(params) ? params : [params];
        if (!fileIdList.length) return [];
        const data = await request({
            method: 'get',
            url: '/gss/v1/file/download/url',
            params: { fileIdList: fileIdList.join(',') },
            headers: { _tenantId: 0, _appId: 0 },
        });

        return (data || []).map((item: any) => item.fileUrl);
    } catch (e) {
        return [];
    }
};

export const isMobile = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;

    // 检查常见的移动端浏览器用户代理字符串
    if (/android/i.test(userAgent)) {
        return 'Android';
    }
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS';
    }
    if (/windows phone/i.test(userAgent)) {
        return 'Windows Phone';
    }
    if (/BlackBerry/i.test(userAgent)) {
        return 'BlackBerry';
    }
    if (/BB10/i.test(userAgent)) {
        return 'BlackBerry 10';
    }
    if (/IEMobile/i.test(userAgent)) {
        return 'Windows Mobile';
    }

    // 如果不是上述移动端设备，返回 false
    return false;
};

export const isIOS = () => {
    const userAgent = navigator.userAgent;

    if (/iPhone|iPad|iPod/i.test(userAgent)) {
        return true;
    } else {
        return false;
    }
};

export function generateUUID(): string {
    let d = new Date().getTime();
    const uuid = 'xxxxxxxxxxxxyxxx'.replace(/[xy]/g, function (c) {
        const r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16);
    });
    return uuid;
}

export const langue = {
    ar_EG: 'العربية',
    bg_BG: 'български',
    ca_ES: 'Català',
    cs_CZ: 'Česky',
    de_DE: 'Deutsch',
    el_GR: 'Ελληνικά',
    en_GB: 'English',
    en_US: 'English',
    es_ES: 'Español',
    et_EE: 'Eestlane',
    fa_IR: 'فارسی',
    fi_FI: 'suomalainen',
    fr_BE: 'Français (Belgique)',
    fr_FR: 'Français',
    he_IL: 'עברית',
    hi_IN: 'हिन्दी',
    hr_HR: 'hrvatski',
    hu_HU: 'magyar',
    is_IS: 'Íslensku',
    id_ID: 'Orang indonesia',
    it_IT: 'Italiano',
    ja_JP: '日本語',
    kn_IN: 'ಕನ್ನಡ',
    ko_KR: '한국어',
    nb_NO: 'norsk språk',
    ne_NP: 'नेपाली',
    nl_BE: 'Nederlands (België)',
    nl_NL: 'Nederlands',
    pl_PL: 'Polski',
    pt_BR: 'Português (Brasil)',
    pt_PT: 'Português',
    sk_SK: 'slovenského jazyk',
    sr_RS: 'Српски',
    sl_SI: 'Slovensko',
    sv_SE: 'svenska',
    ta_IN: 'தமிழ் மொழி',
    th_TH: 'ไทย',
    tr_TR: 'Türk dili',
    ro_RO: 'românesc',
    ru_RU: 'русский',
    uk_UA: 'Українська',
    vi_VN: 'Tiếng Việt Nam',
    zh_CN: '简体中文',
    zh_TW: '繁體中文(台湾)',
    zh_HK: '繁體中文(香港)',
    tj_TJ: 'тоҷикӣ',
};

const getAuthLanguage = (targetLanguage, authLanguageList) => {
    // 转化为小写字母进行匹配，并把 - 统一为 _
    const language = targetLanguage.toLowerCase().replace('-', '_');
    const authList = authLanguageList.map(lang => lang.toLowerCase().replace('-', '_'));
    const [lang, country] = language.split('_');

    // 语言格式为 <语言代码>
    if (!country) {
        const targetIndex = authList.findIndex((i) => i.split('_')[0] === lang);
        return authLanguageList[targetIndex];
    }
    // 语言格式为 <语言代码>_<国家/地区代码>
    if (authList.indexOf(language) !== -1) {
        const targetIndex = authList.indexOf(language);
        return authLanguageList[targetIndex];
    } else {
        // 格式为<语言代码>_<国家/地区代码>但全匹配失败，使用<语言代码>进行匹配
        const targetIndex = authList.findIndex((i) => i.split('_')[0] === lang);
        return authLanguageList[targetIndex];
    }
};

export const getDefaultLang = ({defaultLang = 'en_US',authLanguageList = [],languageInUrl}) => {
    let authLang;
    // url中存在语言参数
    if (languageInUrl) {
        authLang =  getAuthLanguage(languageInUrl,authLanguageList);
        if (authLang) return authLang;
    }

    // 使用用户历史语言
    const historyLang = window.localStorage.getItem('LANG');
    if (historyLang) {
        authLang =  getAuthLanguage(historyLang,authLanguageList);
        if (authLang) return authLang;
    }

    // 使用浏览器的默认语言
    const navigatorLangs = navigator.languages;
    navigatorLangs.some(lang => {
        authLang = getAuthLanguage(lang,authLanguageList);
        return Boolean(authLang);
    });
    if (authLang) return authLang;

    // 直接使用默认的语言
    return defaultLang;
};

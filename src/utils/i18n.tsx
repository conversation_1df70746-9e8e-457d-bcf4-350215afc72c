import i18n, {
    TFunction,
    TFunctionResult,
    TFunctionKeys,
    StringMap,
    TOptions,
} from 'i18next';

interface TTFunction {
    // basic usage
    <
        TResult extends TFunctionResult = string,
        <PERSON><PERSON><PERSON><PERSON> extends TFunctionKeys = string,
        TInterpolationMap extends object = StringMap,
    >(
        key: TKeys | TKeys[],
        options?: TOptions<TInterpolationMap> | string,
    ): TResult;
    // overloaded usage
    <
        TResult extends TFunctionResult = string,
        <PERSON><PERSON><PERSON><PERSON> extends TFunctionKeys = string,
        TInterpolationMap extends object = StringMap,
    >(
        key: TKeys | TKeys[],
        defaultValue?: string,
        options?: TOptions<TInterpolationMap> | string,
    ): TResult | JSX.Element;
}
export const tt: TTFunction = (...args) => {
    const text = Reflect.apply(i18n.t, i18n, args);
    return (
        <div className="text-fade-in" key={text}>
            {text}
        </div>
    );
};

export const noContainerTt: TTFunction = (...args) => {
    const text = Reflect.apply(i18n.t, i18n, args);
    return text;
};

export const t: TFunction = i18n.t;

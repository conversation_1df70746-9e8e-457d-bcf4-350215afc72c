// 默认关闭用户密码找回
export const DEFAULTRETRIEVEPASSWORD = 0;
export enum RETRIEVE_PASSWORD_TYPE {
    'phoneNumber' = '0',
    'mailNumber' = '1',
    'OTPCode' = '2',
}
export const PHONE_AUTH_CODE_LENGTH = 6; // 手机验证码长度
export const EMAIL_AUTH_CODE_LENGTH = 8; // 邮箱验证码长度
export const HALF_HOUR = 1800; // 半小时

export const STEPS = {
    CHECK_ACCOUNT: 1,
    SHOW_FIND_WAY: 2,
    VERIFY_IDENTITY: 3,
    RESET_PASSWORD: 4,
    RESET_SUCCESS: 5,
};
export const REDIRECT_DOWN_TOTAL = 4; // 成功后跳转倒计时
export const COUNT_DOWN_TOTAL = 121; // 121 验证码倒计时
export const BIZ_TYPE = {
    LOGIN: 0,
    OTP_VERIFY: 1,
    VERIFY_IDENTIFY: 2,
    SECOND_VALIDATE: 3,
    FIND_PASSWORD: 4,
};
export const RESPONSE_CODE = {
    USER_UNEXIST: *********,
    AUTH_CODE_ERROR: *********, // 验证码错误
    NOT_GET_AUTH_CODE: [*********, *********, *********], // 输入的邮箱错误、输入的电话错误、验证码错误、未获取验证码
    MAX_TIMES: 1456, // 连续获取验证码超次数
    FAIL_SEND_SMS: 1453, // 短信验证码发送失败
    GET_CODE_LIMIT: *********, // 连续多次输错验证码
    HANDLE_STOP_USER: *********, // 用户已被停用
    AUTO_STOP_USER: *********, // 用户已被自动停用
    USER_EMAIL_REPEAT: *********, // 用户邮箱重复
    USER_PHONE_NUMBER_REPEAT: 120020089, // 用户手机号重复
    OTP_CODE_ERROR_MESSAGE: 120020199, // OTP错误，使用该错误码时，展示后端的提示信息
};

export const LOGINSTYLE1 = 1;
export const LOGINSTYLE2 = 2;
export const LOGINSTYLE3 = 3;
export const LOGINSTYLE4 = 4;
// 右侧样式
export const LOGIN_STYLE5 = 5;
export const LOGIN_STYLE6 = 6;
export const LOGIN_STYLE7 = 7;

export const CUSTOM_AGREEMENT_TYPE = {
    FILE: 1,
    LINK: 2,
};

// 停用
export const STATE_UN_USE = 2;

/**
 * 容器背景的高度:600
 */
export const BG_CONTAINER_HEIGHT = 580;

/**
 * 登录存在logo和文字的高度:500
 */
export const BG_WITH_LOGO_CONTAINER_HEIGHT = 500;

/**
 * @description: 支持空格的特殊字符校验正则,每次执行校验时需要单独生成新的正则，防止被上次校验的lastIndex影响
 */
export const VERIFY_SPECIAL_CHARACTER_REGEXP = /[()`~!@#$%^&*\-+=_|{}[\]:;'<>,\.?/ ]/g;

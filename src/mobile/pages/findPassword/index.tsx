import './index.less';
import { useEffect, useMemo, useRef, useState } from 'react';
import usePhoneEmailUniqueVerify from '@/pc/pages/find-password/hooks/usePhoneEmailUniqueVerify';
import { RESPONSE_CODE, STEPS } from '@/utils/constant';
import Step1 from '@/mobile/pages/findPassword/componnets/Step1';
import Step2 from '@/mobile/pages/findPassword/componnets/Step2';
import { FindWayType, UserInfoProps } from '@/pc/pages/find-password';
import Step3 from '@/mobile/pages/findPassword/componnets/Step3';
import { useAsyncEffect, useUpdate } from '@streamax/hooks';
import i18n from 'i18next';
import { queryLoginConfig } from '@/utils/common';
import Step4 from '@/mobile/pages/findPassword/componnets/Step4';
import Step5 from '@/mobile/pages/findPassword/componnets/Step5';

/**

 *<AUTHOR>

 *@date 2024/11/17 10:30:10

 *@description: add a description

 **/

export default () => {
    // 手机和邮箱是否唯一验证
    const { isNoPEVerify, step, setStep } = usePhoneEmailUniqueVerify();
    const [findWay, setFindWay] = useState<FindWayType>('');
    const [userInfo, setUserInfo] = useState<UserInfoProps>({
        mailNumber: '',
        phoneNumber: '',
        account: '',
        areaCode: Number(window.APP_CONFIG['find.password.areacode']),
    }); // 用户信息;
    const [config, setConfig] = useState<any>(null);
    useEffect(() => {
        // 回退到找回密码流程的第一步(验证账户是否存在页)
        const blackStep = isNoPEVerify
            ? STEPS.CHECK_ACCOUNT
            : STEPS.SHOW_FIND_WAY;
        if (step > blackStep && !window.onpopstate) {
            // 去掉第一步验证账号是否存在后原第二步变为第一步
            window.history.pushState(null, null, document.URL);
            window.onpopstate = function () {
                window.location.reload();
            };
        }
    }, [step]);

    const nextStep = (data: any, stepNow: number) => {
        if (step === STEPS.CHECK_ACCOUNT) {
            setUserInfo(data);
        } else if (step === STEPS.SHOW_FIND_WAY) {
            setFindWay(data.findWay);
        } else if (step === STEPS.VERIFY_IDENTITY) {
            setUserInfo(data);
        }
        setStep(step + 1);
    };

    // 找回密码操作区内容
    const getMainContent = () => {
        switch (step) {
            case STEPS.CHECK_ACCOUNT:
                return <Step1 nextStep={nextStep} />;
            case STEPS.SHOW_FIND_WAY:
                return (
                    <Step2
                        nextStep={nextStep}
                        config={config}
                        userInfo={userInfo}
                        isNoPEVerify={isNoPEVerify}
                    />
                );
            case STEPS.VERIFY_IDENTITY:
                return (
                    <Step3
                        findWay={findWay}
                        nextStep={nextStep}
                        userInfo={userInfo}
                        isNoPEVerify={isNoPEVerify}
                    />
                );
            case STEPS.RESET_PASSWORD:
                return <Step4 nextStep={nextStep} userInfo={userInfo} />;
            case STEPS.RESET_SUCCESS:
                return <Step5 />;
            default:
                return null;
        }
    };
    // 获取登录策略
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        dataConfig = await queryLoginConfig();
        setConfig(dataConfig);
    };
    useAsyncEffect(async () => {
        getLoginConfig();
        document.title = i18n.t('@base:@name__find_password.title', '');
    }, []);

    return <div id={'mobile-find-password-page'}>{getMainContent()}</div>;
};

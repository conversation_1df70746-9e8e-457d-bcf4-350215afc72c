import './index.less';
import FormSelect from '@/mobile/component/FormSelect';
import { Input } from '@arco-design/mobile-react';
import FormVerify from '@/mobile/component/FormVerify';
import { useEffect, useRef, useState } from 'react';
import { showToast } from '@/mobile/util';
import { ValidatorType } from '@arco-design/mobile-utils';
import CustomForm, { CustomFormRef } from '@/mobile/component/CustomForm';
import { FindWayType, UserInfoProps } from '@/pc/pages/find-password';
import { validateEmailOrPhone } from '@/utils/validator';
import { validateAuthCode } from '@/utils/common';
import { tt } from '@/utils/i18n';
import request from '@/utils/request';
import i18n from 'i18next';
import {
    BIZ_TYPE,
    COUNT_DOWN_TOTAL,
    RESPONSE_CODE,
    RETRIEVE_PASSWORD_TYPE,
    STEPS,
} from '@/utils/constant';
import { message } from '@streamax/poppy';

/**

 *<AUTHOR>

 *@date 2024/11/17 10:30:10

 *@description: add a description

 **/
interface StepProps {
    findWay: FindWayType;
    nextStep: (data: any, stepNow: number) => void;
    userInfo: UserInfoProps;
    isNoPEVerify: any;
}
let countDownTimer: any = null;
export default (props: StepProps) => {
    const formRef = useRef<CustomFormRef>(null);
    const [areaCodes, setAreaCodes] = useState<any[]>([]);
    const { findWay, nextStep, userInfo, isNoPEVerify } = props;
    const [errInfo, setErrInfo] = useState<string>();
    const [countdown, setCountDown] = useState(COUNT_DOWN_TOTAL);
    const countdingDown = countdown < COUNT_DOWN_TOTAL;
    const lang = window.localStorage.getItem('LOGIN_LANG') || 'zh_CN';
    const rules = {
        phoneNumberOrMailNumber: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    validateEmailOrPhone(null, val, findWay).catch((msg) => {
                        callback(msg);
                    });
                },
            },
        ],
        authCode: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    validateAuthCode(null, val, findWay).catch((msg) => {
                        callback(msg);
                    });
                },
            },
        ],
    };
    const action = async () => {
        const phoneOrEmail: string =
            formRef.current?.getFieldValue('phoneNumber') ||
            formRef.current?.getFieldValue('mailNumber');
        try {
            await validateEmailOrPhone(null, phoneOrEmail, findWay);
            handleVerifyCodeClick({ phoneOrEmail });
        } catch (msg) {
            showToast(msg.key);
        }
    };
    const handleVerifyCodeClick = async ({ phoneOrEmail }) => {
        if (countdingDown) return;
        const params = {
            bizType: BIZ_TYPE.FIND_PASSWORD,
            [findWay]: phoneOrEmail,
            account: userInfo?.account,
        };
        if (findWay === 'phoneNumber') {
            params.areaCode = userInfo.areaCode;
        }
        try {
            // 开始倒计时
            setCountDown((s) => s - 1);
            countDownTimer = setInterval(() => {
                setCountDown((s) => {
                    if (s === 1) {
                        countDownTimer && clearInterval(countDownTimer);
                        return COUNT_DOWN_TOTAL;
                    } else {
                        return s - 1;
                    }
                });
            }, 1000);
            let rs: any;
            if (findWay === 'phoneNumber') {
                rs = await request.get(
                    `/base-server-service/api/v1/phone/verify`,
                    {
                        params,
                        noDataInterceptor: true,
                        headers: {
                            _langtype: lang,
                        },
                    },
                );
            } else {
                rs = await request.post(
                    `/base-server-service/api/v1/email/verify`,
                    params,
                    {
                        noDataInterceptor: true,
                        headers: {
                            _langtype: lang,
                        },
                    },
                );
            }
            const errorNumberCode =
                findWay === 'mailNumber'
                    ? RESPONSE_CODE.NOT_GET_AUTH_CODE[0] // 输入的邮箱错误
                    : RESPONSE_CODE.NOT_GET_AUTH_CODE[1]; // 输入的电话错误
            if (rs.data.code === errorNumberCode) {
                // 错误提示信息
                const msg =
                    findWay === 'mailNumber'
                        ? tt('@base:@message__reset_password.input.right.email')
                        : tt(
                              '@base:@message__reset_password.input.right.phone',
                          );
                setErrInfo(msg);
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            }
            if (rs.data.code === RESPONSE_CODE.MAX_TIMES) {
                setErrInfo(tt('@base:@message__get_validate.code.times.limit'));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.NOT_GET_AUTH_CODE[2]) {
                // 输入错误邮箱、提示参数错误
                setErrInfo(tt(rs.data.langKey));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.FAIL_SEND_SMS) {
                // 发送短信验证码失败
                setErrInfo(tt('@base:@return__phone_auth_send_error'));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (
                [
                    RESPONSE_CODE.HANDLE_STOP_USER,
                    RESPONSE_CODE.AUTO_STOP_USER,
                ].includes(rs.data.code)
            ) {
                setErrInfo(tt(rs.data.langKey, ''));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (
                [
                    RESPONSE_CODE.USER_EMAIL_REPEAT,
                    RESPONSE_CODE.USER_PHONE_NUMBER_REPEAT,
                ].includes(rs.data.code)
            ) {
                if (RESPONSE_CODE.USER_EMAIL_REPEAT === rs.data.code) {
                    setErrInfo(
                        tt(
                            '@base:@message__get_repeat.code.fail.send.email.repeat',
                        ),
                    );
                } else {
                    setErrInfo(
                        tt(
                            '@base:@message__get_repeat.code.fail.send.phone.repeat',
                        ),
                    );
                }
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else {
                setCountDown((s) => s - 1);
            }
        } catch (error) {
            message.error(error);
        }
    };

    const getVerifyCodeText = () => {
        if (countdingDown) {
            return countdown;
        }
        return null;
    };

    const formItem = [
        findWay === 'phoneNumber' && {
            field: 'areaCode',
            component: <FormSelect dataSource={areaCodes} />,
            key: 'areaCode',
            initialValue: 86,
        },
        findWay === 'phoneNumber' && {
            field: 'phoneNumber',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__login.placeholder.phone',
                        '',
                    )}
                />
            ),
            key: 'phoneNumber',
            rules: rules.phoneNumberOrMailNumber,
        },
        findWay === 'mailNumber' && {
            field: 'mailNumber',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__reset_password.placeholder.email',
                        '',
                    )}
                />
            ),
            key: 'mailNumber',
            rules: rules.phoneNumberOrMailNumber,
        },
        {
            field: 'authCode',
            component: (
                <FormVerify
                    action={action}
                    mode="phoneNumber"
                    count={getVerifyCodeText()}
                />
            ),
            rules: rules.authCode,
            key: 'authCode',
        },
    ].filter(Boolean) as any;

    const onSubmit = async (values: any, result: any) => {
        // 带验证码进行身份验证
        const { phoneNumber, mailNumber, authCode } = values;
        const params: any = {
            verifyType: Number(RETRIEVE_PASSWORD_TYPE[findWay]),
            bizType: BIZ_TYPE.FIND_PASSWORD,
            number: phoneNumber || mailNumber,
            verifyCode: authCode,
            account: userInfo?.account?.trim(),
        };
        if (findWay === 'phoneNumber') {
            params.areaCode = formRef.current?.getFieldValue('areaCode');
        }
        // 用用户信息和验证码进行安全验证
        const rs = await request.post(
            `/base-server-service/api/v1/user/security/verify`,
            params,
            {
                noDataInterceptor: true,
            },
        );
        if (RESPONSE_CODE.NOT_GET_AUTH_CODE.includes(rs.data.code)) {
            // 未点击获取验证码、输入的邮箱、电话不匹配
            setErrInfo(tt('@base:@message__user_not.get.validate.code'));

            formRef.current?.setFieldsValue({
                authCode: undefined,
            });
        } else if (rs.data.code === RESPONSE_CODE.AUTH_CODE_ERROR) {
            setErrInfo(tt('@base:@message__reset_password.auth.code.error'));
        } else if (rs.data.code === RESPONSE_CODE.GET_CODE_LIMIT) {
            setErrInfo(tt('@base:@return__120020030'));
        } else {
            let identifyInfo;
            let pwdConfig;
            const data = rs.data.data;
            if (data) {
                // 存储验证信息，重置密码使用
                identifyInfo = {
                    userId: data.userId,
                    signal: data.signal,
                };

                // setLoading(false);
                const {
                    includeSpecialChar,
                    includeUpperLowerChar,
                    maxDigit,
                    minDigit,
                } = rs.data.data;
                pwdConfig = {
                    includeSpecialChar,
                    includeUpperLowerChar,
                    maxDigit,
                    minDigit,
                };
                nextStep({ identifyInfo, pwdConfig }, STEPS.VERIFY_IDENTITY);
            } else {
                setErrInfo(
                    tt('@base:@message__reset_password.auth.code.error'),
                );
                // setLoading(false);
            }
        }
    };
    useEffect(() => {
        if (errInfo && typeof errInfo.key === 'string') {
            showToast(errInfo.key);
        }
    }, [errInfo]);

    const getAreaCodes = async () => {
        try {
            const data = await request.get(
                '/base-server-service/api/v1/areacode/query',
            );
            const parseData = data.map((item: any) => ({
                value: item.areaCode,
                label:
                    '+' +
                    item.areaCode +
                    ' ' +
                    i18n.t(
                        `@i18n:@areaCode__${item.areaCode}`,
                        item.countryName,
                    ),
            }));
            setAreaCodes(parseData);
        } catch (e) {
            // message.error(e);
        }
    };

    useEffect(() => {
        if (findWay === 'phoneNumber' && !areaCodes.length) {
            getAreaCodes();
            formRef.current?.setFieldsValue({
                areaCode: Number(window.APP_CONFIG['find.password.areacode']),
            });
        }
    }, [findWay]);
    return (
        <div className={'step3-container'}>
            <div className="mobile-find-password-text">
                {tt('@base:@name__find_password.title')}
            </div>
            <div className="mobile-find-password-describe">
                {findWay === 'phoneNumber'
                    ? i18n.t(
                          '@base:@message__reset_password.input.complete.phone',
                          '',
                      )
                    : i18n.t(
                          '@base:@message__reset_password.input.complete.email',
                          '',
                      )}{' '}
                {isNoPEVerify ? (
                    <>
                        &nbsp;
                        <span className="high-light-phone-email">
                            {findWay === 'phoneNumber'
                                ? userInfo.phoneNumber
                                : userInfo.mailNumber}
                        </span>{' '}
                        &nbsp;
                        {i18n.t(
                            '@base:@message__reset_password.use.to.identify',
                            '',
                        )}
                    </>
                ) : null}
            </div>
            <CustomForm
                className={'find-password-form'}
                ref={formRef}
                formItem={formItem}
                submitText={tt('@base:@action__find_password.next.step')}
                formProps={{
                    onSubmit,
                }}
            />
            <div className="mobile-find-password-describe">
                {tt('@base:@message__reset_password.no.verification.code')}
            </div>
            <div className="mobile-error-info">{errInfo}</div>
        </div>
    );
};

import './index.less';
import { Input } from '@arco-design/mobile-react';
import { useEffect, useRef, useState } from 'react';
import { showToast } from '@/mobile/util';
import { ValidatorType } from '@arco-design/mobile-utils';
import CustomForm, {
    CustomFormRef,
    FormItem,
} from '@/mobile/component/CustomForm';
import { tt } from '@/utils/i18n';
import i18n from 'i18next';
import request from '@/utils/request';
import { encryptors, validatePassword } from '@/utils/common';
import { message } from '@streamax/poppy';
// @ts-ignore
import md5 from 'js-md5';
import { STEPS } from '@/utils/constant';
const encryptor = encryptors();
/**

 *<AUTHOR>

 *@date 2024/11/17 10:30:10

 *@description: add a description

 **/
interface StepProps {
    nextStep: (data: any, stepNow: number) => void;
    userInfo: any;
}

export default (props: StepProps) => {
    const { nextStep, userInfo } = props;
    const formRef = useRef<CustomFormRef>(null);
    const [errInfo, setErrInfo] = useState<string>();
    const [pwdConfig, setPwdConfig] = useState<any>();
    const [identifyInfo, setIdentifyInfo] = useState<any>();
    console.log(22222, userInfo);
    // {
    //     "identifyInfo": {
    //     "userId": "3131942770155988674",
    //         "signal": "be0b7a5bce8b4c4eae4794d2971d73ee"
    // },
    //     "pwdConfig": {
    //     "includeSpecialChar": 1,
    //         "includeUpperLowerChar": 1,
    //         "maxDigit": 20,
    //         "minDigit": 8
    // }
    // }
    useEffect(() => {
        if (userInfo) {
            setIdentifyInfo(userInfo.identifyInfo);
            setPwdConfig(userInfo.pwdConfig);
        }
    }, [userInfo]);

    const rules = {
        newPassword: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__reset_password.placeholder.input.password.again',
                            ),
                        );
                    } else {
                        const {
                            includeSpecialChar,
                            includeUpperLowerChar,
                            maxDigit,
                            minDigit,
                        } = pwdConfig;
                        validatePassword(val, {
                            includeSpecialChar,
                            includeUpperLowerChar,
                            maxDigit,
                            minDigit,
                        }).catch((msg) => {
                            callback(msg);
                        });
                    }
                },
            },
        ],
        confirmPassword: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__reset_password.placeholder.input.password.again',
                            ),
                        );
                    }
                    if (
                        !val ||
                        formRef.current?.getFieldValue('newPassword') === val
                    ) {
                        return;
                    }
                    callback(
                        tt(
                            '@base:@message__update_password.message.same_password',
                        ),
                    );
                },
            },
        ],
    };

    const formItem: FormItem[] = [
        {
            field: 'newPassword',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__update_password.placeholder.new_password',
                        '',
                    )}
                    maxLength={20}
                    type={'password'}
                />
            ),
            key: 'newPassword',
            rules: rules.newPassword,
        },
        {
            field: 'confirmPassword',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__reset_password.placeholder.input.password.again',
                        '',
                    )}
                    maxLength={20}
                    type={'password'}
                />
            ),
            key: 'confirmPassword',
            rules: rules.confirmPassword,
        },
    ];

    const onSubmit = async (values: any, result: any) => {
        const { newPassword, confirmPassword } = values;
        const { userId, signal } = identifyInfo;
        const resetParams = {
            userId,
            signal,
            newPwd: encryptor.encrypt(md5(newPassword)),
        };
        try {
            // 重置密码
            const res = await request({
                method: 'post',
                url: '/base-server-service/api/v1/user/retrieve',
                data: resetParams,
            });
            if (res) {
                nextStep({}, STEPS.RESET_PASSWORD);
                // setLoading(false);
            }
        } catch (error) {
            message.error(error);
            // setLoading(false);
        }
    };

    useEffect(() => {
        if (errInfo && typeof errInfo.key === 'string') {
            showToast(errInfo.key);
        }
    }, [errInfo]);

    return (
        <div className={'step4-container'}>
            <div className="mobile-find-password-text">
                {tt('@base:@name__reset_password.title')}
            </div>
            <CustomForm
                className={'find-password-form'}
                ref={formRef}
                formItem={formItem}
                submitText={tt(
                    '@base:@action__update_password.messagebox.confirm',
                )}
                formProps={{
                    onSubmit,
                }}
            />
            <div className="mobile-error-info">{errInfo}</div>
        </div>
    );
};

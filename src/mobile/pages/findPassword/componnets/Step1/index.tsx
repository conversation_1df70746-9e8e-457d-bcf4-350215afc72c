import { useLocation } from 'umi';
import './index.less';
import FormSelect from '@/mobile/component/FormSelect';
import { Input } from '@arco-design/mobile-react';
import FormVerify from '@/mobile/component/FormVerify';
import FormAgreementCheckBox from '@/mobile/component/FormAgreementCheckBox';
import { useEffect, useMemo, useRef, useState } from 'react';
import { showToast } from '@/mobile/util';
import { ValidatorType } from '@arco-design/mobile-utils';
import CustomForm, {
    CustomFormRef,
    FormItem,
} from '@/mobile/component/CustomForm';
import { tt } from '@/utils/i18n';
import './index.less';
import i18n from 'i18next';
import { RESPONSE_CODE, STATE_UN_USE, STEPS } from '@/utils/constant';
import request from '@/utils/request';

/**

 *<AUTHOR>

 *@date 2024/11/17 10:30:10

 *@description: add a description

 **/
interface StepProps {
    nextStep: (data: any, stepNow: number) => void;
}
export default (props: StepProps) => {
    const { nextStep } = props;
    const formRef = useRef<CustomFormRef>(null);
    const [errInfo, setErrInfo] = useState<string>();

    const rules = {
        account: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__login.required.message.username',
                            ),
                        );
                    }
                },
            },
        ],
    };

    const formItem: FormItem[] = [
        {
            field: 'account',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__login.placeholder.username',
                        '',
                    )}
                />
            ),
            key: 'account',
            rules: rules.account,
        },
    ];

    const onSubmit = async (values: any, result: any) => {
        // 校验账户是否存在
        const { account } = values;
        // setLoading(true);
        try {
            const params = {
                account: account.trim(),
            };
            // 查询账户是否存在
            const { data } = await request.get(
                `/base-server-service/api/v1/user/exists`,
                {
                    params,
                    noDataInterceptor: true,
                },
            );
            // 用户不存在。修改表单状态
            if (data.code === RESPONSE_CODE.USER_UNEXIST) {
                setErrInfo(
                    tt('@base:@message__reset_password.check.account.exist'),
                );
                // setLoading(false);
                // TODO
            } else {
                const info = data.data;
                if (info?.state === STATE_UN_USE) {
                    // 停用
                    setErrInfo(tt('@base:@return__120020075'));
                    // setLoading(false);
                    return;
                }
                info.mailNumber = info.emailNumber;
                nextStep(info, STEPS.CHECK_ACCOUNT);
            }
        } catch (error) {
            // setLoading(false);
        }
    };

    useEffect(() => {
        if (errInfo && typeof errInfo.key === 'string') {
            showToast(errInfo.key);
        }
    }, [errInfo]);

    return (
        <div className={'step1-container'}>
            <div className="mobile-find-password-text">
                {tt('@base:@name__find_password.title')}
            </div>
            <div className="mobile-find-password-describe">
                {i18n.t('@base:@message__reset_password.input.account', '')}
            </div>
            <CustomForm
                className={'find-password-form'}
                ref={formRef}
                formItem={formItem}
                submitText={tt('@base:@action__find_password.next.step')}
                formProps={{
                    onSubmit,
                }}
            />
            <div className="mobile-error-info">{errInfo}</div>
        </div>
    );
};

import mobileIcon from '@/assets/mobile/icon_mobile_phone.svg';
import emailIcon from '@/assets/mobile/icon_mailbox_fill.svg';
import './index.less';
import { RETRIEVE_PASSWORD_TYPE, STEPS } from '@/utils/constant';
import { tt } from '@/utils/i18n';
import i18n from 'i18next';

/**

 *<AUTHOR>

 *@date 2024/11/17 10:30:10

 *@description: add a description

 **/
interface StepProps {
    nextStep: (data: any, stepNow: number) => void;
    config: any;
    userInfo: any;
    isNoPEVerify: any;
}

export default (props: StepProps) => {
    const { nextStep, config, userInfo, isNoPEVerify } = props;
    const updateWay = [
        {
            name: tt('@base:@message__reset_password.validate.ways.phone'),
            unbindTextCode: '@base:@message__reset_password.no.bind.phone',
            textCode:
                '@base:@message__reset_password.find.way.card.context.phone',
            imgUrl: mobileIcon,
            key: 'phoneNumber',
        },
        {
            name: tt('@base:@name__second_validate.way.label.email'),
            unbindTextCode: '@base:@message__reset_password.no.bind.email',
            textCode:
                '@base:@message__reset_password.find.way.card.context.email',
            imgUrl: emailIcon,
            key: 'mailNumber',
        },
    ];
    const chooseFindWay = (findWay: string) => {
        nextStep({ findWay: findWay }, STEPS.SHOW_FIND_WAY);
    };
    return (
        <div className="step2-container">
            <div className="mobile-update-password-text">
                {tt('@base:@name__find_password.title')}
            </div>
            <div className="mobile-update-password-describe">
                {i18n.t('@base:@message__reset_password.identity.confirm', '')}
            </div>
            {updateWay.map(
                (i) =>
                    config?.retrievePasswordType?.includes(
                        // 绑定了电话/邮箱
                        RETRIEVE_PASSWORD_TYPE[i.key],
                    ) && (
                        <div
                            className={`mobile-update-way ${
                                // 在管理后台开启了对应的找回方式
                                userInfo[i.key] || !isNoPEVerify
                                    ? 'card-item-active'
                                    : 'card-item-disable'
                            }`}
                            key={i.key}
                            onClick={() => {
                                (userInfo[i.key] || !isNoPEVerify) &&
                                    config?.retrievePasswordType.includes(
                                        RETRIEVE_PASSWORD_TYPE[i.key],
                                    ) &&
                                    chooseFindWay(i.key);
                            }}
                        >
                            <img
                                className="mobile-update-way-img"
                                src={i.imgUrl}
                                alt=""
                            />
                            <div className="mobile-update-way-name">
                                <span>{i.name}</span>
                                {isNoPEVerify ? (
                                    <span className="find-way-card-text">
                                        {userInfo[i.key]
                                            ? i18n.t(i.textCode, {
                                                  [i.key]:
                                                      i.key === 'phoneNumber'
                                                          ? userInfo[i.key]
                                                          : userInfo[i.key],
                                              })
                                            : i18n.t(i.unbindTextCode)}
                                    </span>
                                ) : null}
                            </div>
                        </div>
                    ),
            )}
        </div>
    );
};

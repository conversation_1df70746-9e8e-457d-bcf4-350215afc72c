.step2-container {
    .mobile-update-password-text {
        height: 0.64rem;
        margin-top: 2.32rem;
        color: #000000d9;
        font-weight: 600;
        font-size: 0.48rem;
        line-height: 0.64rem;
        text-align: center;
    }
    .mobile-update-password-describe {
        margin-top: 0.64rem;
        margin-bottom: 0.32rem;
        color: #00000073;
        font-size: 0.28rem;
        line-height: 0.44rem;
    }
    .mobile-update-way {
        display: flex;
        gap: 0.48rem;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.48rem;
        padding: 0.48rem;
        text-align: center;
        background-color: #fff;
        border: 0.02rem solid #0000000f;
        border-radius: 0.16rem;
        .mobile-update-way-img {
            width: 1.08rem;
            height: 1.08rem;
        }
        .mobile-update-way-name {
            display: flex;
            flex: 1;
            flex-direction: column;
            color: #000000d9;
            font-weight: 600;
            font-size: 0.36rem;
            line-height: 0.52rem;
            text-align: left;
            .find-way-card-text {
                color: #00000073;
                font-size: 0.28rem;
                word-break: break-all;
            }
        }
    }
    .card-item-disable {
        background: #0000000a;
        .mobile-update-way-img {
            color: #00000040;
        }
    }
}

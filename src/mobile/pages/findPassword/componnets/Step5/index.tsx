import { IconSuccessFill } from '@streamax/poppy-icons';
import i18n from 'i18next';

import { tt } from '@/utils/i18n';
import { useEffect, useRef, useState } from 'react';
import { REDIRECT_DOWN_TOTAL, STEPS } from '@/utils/constant';
import { history } from 'umi';
import './index.less';
import { Button } from '@arco-design/mobile-react';

/**

 *<AUTHOR>

 *@date 2024/11/21 10:34:43

 *@description: add a description

 **/
export default () => {
    const [redirectDown, setRedirectDown] = useState(REDIRECT_DOWN_TOTAL);
    const timeOutRef: any = useRef(); // 重置成功后跳转时间
    const query = history.location?.query || {};
    const redirect_url = query.redirect_url as string;
    useEffect(() => {
        // 重置成功
        setRedirectDown((s) => s - 1);
        timeOutRef.current = setInterval(() => {
            setRedirectDown((s) => {
                if (s === 1) {
                    timeOutRef.current && clearInterval(timeOutRef.current);
                    redirectToLogin();
                    return '';
                } else {
                    return s - 1;
                }
            });
        }, 1000);
    }, []);

    // 跳转登陆
    const redirectToLogin = () => {
        clearTimeout(timeOutRef.current);
        const url = redirect_url
            ? `/login/pc/login?redirect_url=${redirect_url}`
            : '/login/pc/login';
        const loginUrl = `${location.origin}${url}`;
        window.localStorage.removeItem('AUTH_FIND_PASSWORD_TOKEN');
        window.location.href = loginUrl;
    };
    return (
        <div className="step5-container">
            <div className="icon">
                <IconSuccessFill />
            </div>
            <div className="mobile-find-password-text">
                {i18n.t('@base:@message__reset_password.success', '')}
            </div>
            <div className="mobile-find-password-describe">
                {redirectDown +
                    i18n.t(
                        '@base:@action__reset_password.redirect.login.seconds',
                        '',
                    )}
            </div>
            <Button className="mobile-custom-submit" onClick={redirectToLogin}>
                {tt('@base:@action__reset_password.login.right.now')}
            </Button>
        </div>
    );
};

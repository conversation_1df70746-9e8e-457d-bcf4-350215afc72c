// @ts-nocheck
import { useState, useEffect, useRef, useMemo } from 'react';
import { history } from 'umi';
import request from '@/utils/request';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { message } from '@streamax/poppy';
import {
    validateAuthCode,
} from '@/utils/common';
import { useAsyncEffect } from '@streamax/hooks';
import qs from 'querystring';
import {
    PHONE_AUTH_CODE_LENGTH,
    EMAIL_AUTH_CODE_LENGTH,
    RETRIEVE_PASSWORD_TYPE,
    COUNT_DOWN_TOTAL,
    BIZ_TYPE,
    RESPONSE_CODE,
} from '../../../utils/constant';
import './index.less';
import CustomForm, { CustomFormRef } from '@/mobile/component/CustomForm';
import FormSelect from '@/mobile/component/FormSelect';
import FormVerify from '@/mobile/component/FormVerify';
import { ValidatorType } from '@arco-design/mobile-utils';
import { showToast } from '@/mobile/util';
import OTPConfirmModal from "@/mobile/pages/second-validate/components/OTPConfirmModal";
import {getUserInfo} from "@/service/login";
import {isNil} from "@streamax/poppy/es/starry-tree-select/components/rc-tree-select/src/utils/valueUtil";

interface UserInfoProps {
    phoneNumber: string;
    mailNumber: string;
    account: string;
    areaCode: string;
    userId: string;
    tenantId;
}

type FindWayProps = 'mailNumber' | 'phoneNumber' | "OTPCode";

let countDownTimer: any = null;
export default () => {
    const formRef = useRef<CustomFormRef>(null);
    const lang = window.localStorage.getItem('LOGIN_LANG') || 'zh_CN';
    const [userInfo, setUserInfo] = useState<UserInfoProps>(); // 登陆成功后返回的用户信息
    const [OTPConfirmModalVisible, setOTPConfirmModalVisible] = useState<boolean>(false);
    const [emailNumber, setEmailNumber] = useState<string>();

    useEffect(() => {
        let info: any = history.location.query;
        info = {
            ...info,
            areaCode: info?.areaCode === 'null' ? null : info?.areaCode,
        };
        setUserInfo(info);
    }, []);

    const [findWay, setFindWay] = useState<FindWayProps>();
    const [countdown, setCountDown] = useState(COUNT_DOWN_TOTAL);
    const [success, setSuccess] = useState<boolean>(false);
    const [number, setNumber] = useState<string>(); // 未脱敏手机或邮箱
    const [errInfo, setErrInfo] = useState<string>();

    const optionsMap = {
        '0': {
            label:
                userInfo?.phoneNumber !== 'null' // 绑定了电话
                    ? `+${userInfo?.areaCode} ` + userInfo?.phoneNumber
                    : tt('@base:@name__second_validate.way.label.phone'),
            value: 'phoneNumber',
        },
        '1': {
            label:
                userInfo?.mailNumber !== 'null' // 绑定了邮箱
                    ? userInfo?.mailNumber
                    : tt('@base:@name__second_validate.way.label.email'),
            value: 'mailNumber',
        },
        '2': {
            label: tt('@base:@name__second_validate.way.label.OTP'),
            value: 'OTPCode',
        },
    };
    const validateOptions = [];
    for (const key in optionsMap) {
        if (userInfo?.verificationMode?.includes(key)) {
            validateOptions.push(optionsMap[key]);
        }
    }

    useAsyncEffect(async () => {
        if ((findWay === 'OTPCode' || findWay === 'mailNumber') && !emailNumber && !isNil(userInfo?.userId) && !isNil(userInfo?.tenantId)){
            const email: string = await getUserInfoByField('email');
            setEmailNumber(email);
        }
    }, [findWay, userInfo]);

    useEffect(() => {
        if (errInfo && typeof errInfo.key === 'string') {
            showToast(errInfo.key);
        }
    }, [errInfo]);

    useEffect(() => {
        let way: string | undefined;

        if (
            userInfo?.mailNumber !== 'null' &&
            userInfo?.verificationMode?.includes('1') &&
            userInfo?.defaultMode?.includes('1')
        ) {
            way = 'mailNumber';
        } else if (
            userInfo?.phoneNumber !== 'null' &&
            userInfo?.verificationMode?.includes('0') &&
            userInfo?.defaultMode?.includes('0')
        ) {
            way = 'phoneNumber';
        } else if (
            userInfo?.OTPCode !== 'null' &&
            userInfo?.verificationMode?.includes('2') &&
            userInfo?.defaultMode?.includes('2')
        ) {
            way = 'OTPCode';
        } else {
            way = undefined;
        }
        setFindWay(way);
        formRef.current?.setFieldsValue({
            validateWay: way,
        });
    }, [userInfo]);

    const countdingDown = countdown < COUNT_DOWN_TOTAL;

    useAsyncEffect(async () => {
        document.title = i18n.t('@base:@name__second_validate.title', '');
    }, []);

    // 发送验证码按钮文本
    const getVerifyCodeText = () => {
        if (countdingDown) {
            return countdown;
        }
        return null;
    };


    const getUserInfoByField = async (field: string)=>{
        return await getUserInfo(
            {
                userId: userInfo?.userId,
                searchField: field,
            },
            {
                _tenantId: userInfo?.tenantId,
            },
        );
    };
    // 获取验证码
    const handleVerifyCodeClick = async (type: string) => {
        if (countdingDown) return;
        setCountDown((s) => s - 1);
        countDownTimer = setInterval(() => {
            setCountDown((s) => {
                if (s === 1) {
                    countDownTimer && clearInterval(countDownTimer);
                    return COUNT_DOWN_TOTAL;
                } else {
                    return s - 1;
                }
            });
        }, 1000);
        let data: string;
        if (type === 'mailNumber' && emailNumber){
            data = emailNumber;
            setNumber(emailNumber);
        }else {
            data = await getUserInfoByField(type === 'mailNumber' ? 'email' : 'phoneNumber');
            setNumber(data);
        }
        const params = {
            bizType: BIZ_TYPE.SECOND_VALIDATE,
            [type]: data,
            account: userInfo?.account,
        };
        if (type === 'phoneNumber') {
            params.areaCode = userInfo?.areaCode;
        }
        let rs: any;
        try {
            findWay === 'phoneNumber'
                ? (rs = await request.get(
                      `/base-server-service/api/v1/phone/verify`,
                      {
                          params,
                          noDataInterceptor: true,
                          headers: {
                              _langtype: lang,
                          },
                      },
                  ))
                : (rs = await request.post(
                      `/base-server-service/api/v1/email/verify`,
                      params,
                      {
                          noDataInterceptor: true,
                          headers: {
                              _langtype: lang,
                          },
                      },
                  ));
            if (rs.data.code === RESPONSE_CODE.MAX_TIMES) {
                setErrInfo(tt('@base:@message__get_validate.code.times.limit'));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.FAIL_SEND_SMS) {
                setErrInfo(tt('@base:@message__get_auth.code.fail.send.sms'));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            } else if (rs.data.code === RESPONSE_CODE.NOT_GET_AUTH_CODE[0]) {
                setErrInfo(tt(rs.data.langKey));
                setCountDown(COUNT_DOWN_TOTAL + 1);
                countDownTimer && clearInterval(countDownTimer);
            }
        } catch (error) {
            message.error(error);
        }
    };
    // 处理验证方式改变
    const handleWaySelect = (way: 'mailNumber' | 'phoneNumber' | 'OTPCode') => {
        if (['null', null].includes(userInfo[way])) {
            const msg =
                way === 'mailNumber'
                    ? tt('@base:@message__second_validate.safety.user.email')
                    : tt('@base:@message__second_validate.safety.user.phone');
            showToast(msg.key);
            formRef.current?.resetFields();
            return;
        }
        setFindWay(way);
    };

    // 安全验证
    const onSubmit = async (values: any, result: any) => {
        const { authCode } = values;
        let params: any;
        if (findWay === 'OTPCode') {
            params = {
                verifyType: Number(RETRIEVE_PASSWORD_TYPE[findWay]),
                bizType: BIZ_TYPE.SECOND_VALIDATE, // 二次验证
                number: userInfo?.userId,
                verifyCode: authCode,
                account: userInfo?.account || 'streamax',
            };
        }else {
            params = {
                verifyType: Number(RETRIEVE_PASSWORD_TYPE[findWay]),
                bizType: BIZ_TYPE.SECOND_VALIDATE, // 二次验证
                number,
                verifyCode: authCode,
                account: userInfo?.account || 'streamax',
            };
            if (findWay === 'phoneNumber') {
                params.areaCode = userInfo?.areaCode;
            }
        }

        request
            .post(`/base-server-service/api/v1/user/security/verify`, params, {
                noDataInterceptor: true,
            })
            .then(async (rs: any) => {
                const { data } = rs;
                if (data.code === 200) {
                    setSuccess(true);
                    // 跳转主页
                    goMainPage();
                } else if (data.code === RESPONSE_CODE.GET_CODE_LIMIT) {
                    setErrInfo(tt('@base:@return__120020030'));
                } else if (
                    findWay === 'OTPCode' && data.code !== RESPONSE_CODE.OTP_CODE_ERROR_MESSAGE
                ) {
                    setErrInfo(tt('@base:@message__second_validate.otp.validate.error',''));
                }else if (data.code === RESPONSE_CODE.OTP_CODE_ERROR_MESSAGE){
                    showToast(tt(data.langKey).key);
                }
                else {
                    setErrInfo(
                        tt('@base:@message__reset_password.auth.code.error'),
                    );
                }
            });
    };
    // 按钮内容
    const getBtnText = () => {
        if (success) {
            return tt('@base:@message__second_validate.safety.success');
        } else {
            return tt('@base:@message__second_validate.safety.validate');
        }
    };
    // 获取跳转路径
    function getRedirectUrl(url: any) {
        const whiteList = ['localhost', '127.0.0.1'];
        let flag = false;
        whiteList.forEach((item) => {
            url.indexOf(item) > -1 && (flag = true);
        });
        if (flag) {
            return url;
        }
        if (url !== window.location.hostname) {
            return '/';
        }
        return url;
    }
    // 二次验证通过后跳转
    const goMainPage = () => {
        const voucher = sessionStorage.getItem('second-validate-voucher');
        window.localStorage.setItem('AUTH_VOUCHER', voucher);
        window.localStorage.setItem('LANG', lang);
        const query = history.location.query || {};
        const redirect_url = query.redirect_url as string;
        if (redirect_url && redirect_url !== 'undefined') {
            const urlInfo = new URL(redirect_url);
            const url = getRedirectUrl(urlInfo.hostname);
            if (url === '/') {
                window.location.replace(
                    `${window.location.origin}?auth_voucher=${voucher}`,
                );
                return;
            }
            const urlQuery = qs.parse(urlInfo.search.replace('?', ''));

            urlQuery.auth_voucher = voucher;
            urlQuery.lang = lang;
            window.location.replace(
                `${urlInfo.origin}${urlInfo.pathname}?${qs.stringify(
                    urlQuery,
                )}${urlInfo.hash}`,
            );
            return;
        }
        window.location.replace(
            `${window.location.origin}?auth_voucher=${voucher}`,
        );
    };

    const rules = {
        validateWay: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val) {
                        callback(
                            tt(
                                '@base:@message__second_validate.choose.way.message',
                            ),
                        );
                    }
                },
            },
        ],
        authCode: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    validateAuthCode(null, val, findWay).catch((msg) => {
                        callback(msg);
                    });
                },
            },
        ],
    };

    const action = async () => {
        const validateWay: string =
            formRef.current?.getFieldValue('validateWay');
        if (validateWay) {
            handleVerifyCodeClick(findWay);
        } else {
            showToast(
                tt('@base:@message__second_validate.choose.way.message').key,
            );
        }
    };

    const formItem = [
        {
            field: 'validateWay',
            component: (
                <FormSelect
                    dataSource={validateOptions}
                    placeholder={
                        !userInfo?.mailNumber && !userInfo?.phoneNumber
                            ? tt(
                                  '@base:@message__second_validate.choose.way.message',
                              ).key
                            : tt(
                                  '@base:@message__second_validate.choose.way.message',
                              ).key
                    }
                    onChange={handleWaySelect}
                />
            ),
            key: 'validateWay',
            rules: rules.validateWay,
        },
        {
            field: 'authCode',
            component: (
                <FormVerify
                    action={action}
                    mode={findWay === 'OTPCode' ? "OTPCode" : "phoneNumber"}
                    count={getVerifyCodeText()}
                    inputProps={{
                        maxLength:
                            findWay === 'phoneNumber'
                                ? PHONE_AUTH_CODE_LENGTH
                                : EMAIL_AUTH_CODE_LENGTH,
                        placeholder:findWay === 'OTPCode' ? i18n.t('@base:@message__second_validate.otp.placeholder','') : i18n.t(
                            '@base:@message__login.placeholder.auth_code',
                            '',
                        )
                    }}
                />
            ),
            rules: rules.authCode,
            key: 'authCode',
        },
    ].filter(Boolean) as any;

    return (
        <div id="second-validate-mobile">
            <div className="mobile-second-validate-text">
                {tt('@base:@name__second_validate.title')}
            </div>
            <div className="mobile-second-validate-describe">
                {findWay === 'OTPCode' ? tt('@base:@message__second_validate.otp.validate.title','') : tt('@base:@name__second_validate.pc.tip.message')}
            </div>
            <CustomForm
                className={'second-validate-form'}
                ref={formRef}
                formItem={formItem}
                submitText={getBtnText()}
                formProps={{
                    onSubmit,
                }}
            />
            <div className="mobile-second-validate-describe">
                {findWay === "OTPCode" && (
                    <span className={'otp-binding-tips'}>
                        <span>
                            {tt('@base:@message__second_validate.otp.tips.not.binding','')}
                        </span>
                        <a onClick={()=>{
                                if (!emailNumber){
                                    showToast(tt('@base:@message__second_validate.otp.tips.not.binding.email').key);
                                }else {
                                    setOTPConfirmModalVisible(true);
                                }
                            }}>{
                            tt('@base:@message__second_validate.otp.tips.click.here','')}
                        </a>
                        <span>
                            {tt('@base:@message__second_validate.otp.tips.binding','')}
                        </span>
                    </span>
                )}
                {tt('@base:@message__reset_password.no.verification.code')}
            </div>
            <div className="mobile-error-info">{errInfo}</div>
            <OTPConfirmModal
                visible={OTPConfirmModalVisible}
                onClose={()=>{setOTPConfirmModalVisible(false);}}
                userInfo={userInfo}
                fullEmail={emailNumber}
                lang={lang}
            />
        </div>
    );
};

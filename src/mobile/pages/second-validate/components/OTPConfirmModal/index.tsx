
import {tt} from "@/utils/i18n";
import "./index.less";
import InfoBack from "@/components/InfoBack";
import {useEffect, useState, useRef} from "react";
import type {CustomFormRef} from "@/mobile/component/CustomForm";
import CustomForm from "@/mobile/component/CustomForm";
import { Dialog, Input, Loading } from "@arco-design/mobile-react";
import {generateOTPkey, sendEmailToUser} from "@/service/login";
import {BIZ_TYPE} from "@/utils/constant";
import {showToast} from "@/mobile/util";

interface IOTPConfirmModalProps {
    visible: boolean;
    fullEmail: string;
    userInfo: {
        userId: string;
        account: string;
        mailNumber: string;
    },
    lang: string;
    onClose: () => void;
}

export default (props: IOTPConfirmModalProps)=>{
    const { visible, onClose, userInfo, fullEmail, lang } = props;
    const formRef = useRef<CustomFormRef>(null);
    const [count,setCount] = useState(60);
    const [loading,setLoading] = useState(false);
    const [isCountingDown, setIsCountingDown] = useState(false);
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    const onModalConfirm = async ()=>{
        setLoading(true);
        try {
            const OTPKey = await generateOTPkey({
                userId: userInfo.userId
            });
            const url = `${window.location.origin}/account/otp/bind?notlogin=true&OTPKey=${OTPKey}?lang=${lang}`;
            await sendEmailToUser(
                {
                    bizType: BIZ_TYPE.OTP_VERIFY,
                    mailNumber: fullEmail,
                    account: userInfo?.account,
                    otpBindUrl: `<a clicktracking=off href='${url}'>${url}</a>`,
                },
                {
                    _langtype: lang,
                }
            );
            setLoading(false);
            setIsCountingDown(true);
            setCount(60);
        // 开始倒计时
        timerRef.current = setInterval(() => {
            setCount(prevCount => {
                if (prevCount <= 1) {
                    setIsCountingDown(false);
                    if (timerRef.current) {
                        clearInterval(timerRef.current);
                        timerRef.current = null;
                    }
                    return 60;
                }
                return prevCount - 1;
            });
        }, 1000);
        }catch (msg){
            showToast(msg);
            setLoading(false);
        }
        return true;
    };

    // 清理定时器
    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, []);

    // 当模态框关闭时重置状态
    useEffect(() => {
        if (!visible) {
            setIsCountingDown(false);
            setCount(60);
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        }
    }, [visible]);

    useEffect(() => {
        if (userInfo?.mailNumber && formRef.current) {
            formRef.current.setFieldsValue({
                email: userInfo?.mailNumber
            });
        }
    }, [userInfo?.mailNumber, visible]);

    const formItem = [{
        field: 'email',
        component: <Input/>,
        key: 'email',
        disabled: true,
        initialValue: userInfo?.mailNumber,
        noStyle: true
    }];

    const getOkContent = ()=>{
        if (loading) return <Loading type="spin" />;
        if (isCountingDown) {
            return `${count}S`;
        }
        return tt('@base:@action__login.active.modal.ok', '确定');
    };

    return  <Dialog
        className={'OTP-confirm-modal-mobile'}
        visible={visible}
        title={tt("@base:@message__second_validate.otp.validate.modal.title")}
        close={onClose}
        footer={[
            {
                content: tt('@base:@action__login.active.modal.cancel', '取消'),
                onClick: onClose
            },
            {
                content: getOkContent(),
                onClick: onModalConfirm,
                disabled: loading || isCountingDown,
                className: loading || isCountingDown ? 'disable-btn' : ''
            },
        ]}
    >
        <InfoBack className={'mobile-info-back'} title={tt('@base:@message__second_validate.otp.validate.modal.info','')}/>
        <CustomForm
            className={'otp-confirm-form'}
            ref={formRef}
            formItem={formItem}
            submitText={false}
            formProps={{
            }}
        />
    </Dialog>;
};

#second-validate-mobile {
    height: 100%;
    padding: 0 0.64rem;
    overflow: auto;
    background-image: url('../../../assets/mobile/bg.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    .mobile-second-validate-text {
        margin-top: 3rem;
        color: #000000d9;
        font-weight: 600;
        font-size: 0.48rem;
        line-height: 0.64rem;
        text-align: center;
    }
    .mobile-second-validate-describe {
        margin-top: 0.64rem;
        color: #00000073;
        font-size: 0.28rem;
        line-height: 0.44rem;
    }
    .second-validate-form {
        margin-top: 0.32rem;
    }
    .mobile-error-info {
        margin-top: 0.36rem;
        color: #ff4d4f;
        font-size: 0.28rem;
        text-align: center;
    }
    .otp-binding-tips{
        .text-fade-in{
            display: inline;
        }
    }
}

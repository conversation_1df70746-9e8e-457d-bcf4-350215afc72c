#mobile-login-page {
    height: 100%;
    padding: 0 0.64rem;
    overflow: auto;
    background-image: url('../../../assets/mobile/bg.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    .mobile-login-text {
        margin-top: 1.48rem;
        color: #000000d9;
        font-weight: 600;
        font-size: 0.48rem;
        line-height: 0.64rem;
        text-align: center;
    }

    .mobile-login-form {
        margin-top: 0.64rem;
    }

    .forget-password {
        display: flex;
        justify-content: end;
        margin-top: 0.32rem;
        font-size: 0.28rem;
        line-height: 0.44rem;
    }
    .switch-login-way-container {
        display: flex;
        justify-content: center;
        margin-top: 0.64rem;
        font-size: 0.28rem;
        line-height: 0.44rem;
        .switch-login-way {
            display: flex;
            gap: 0.16rem;
            align-items: center;
            font-size: 0.28rem;
            line-height: 0.44rem;
            .mobile-user-img {
                width: 0.28rem;
                height: 0.28rem;
                margin-bottom: 0.04rem;
            }
        }
    }

    .mobile-error-info {
        margin-top: 0.36rem;
        color: #ff4d4f;
        font-size: 0.28rem;
        text-align: center;
    }
    .copyright-container {
        margin-top: 0.64rem;
        color: #00000073;
        font-size: 0.28rem;
        line-height: 0.44rem;
        text-align: center;
        .copyright-text {
            word-break: break-word;
        }
        a {
            color: #00000073;
        }
    }
}

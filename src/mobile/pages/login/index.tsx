/**

 *<AUTHOR>

 *@date 2024/11/14 12:52:38

 *@description: add a description

 **/
import './index.less';
import { Dialog, Input } from '@arco-design/mobile-react';
import userIcon from '@/assets/mobile/icon_user_01_fill.svg';
import mobileIcon from '@/assets/mobile/icon_mobile_phone.svg';
import { useEffect, useRef, useState } from 'react';
import FormSelect, { FormSelectRef } from '@/mobile/component/FormSelect';
import FormAgreementCheckBox from '@/mobile/component/FormAgreementCheckBox';
import { ValidatorType } from '@arco-design/mobile-utils';
import FormVerify, { FormVerifyRef } from '@/mobile/component/FormVerify';
import { showToast } from '@/mobile/util';
import { useHistory } from 'umi';
import CustomForm, { CustomFormRef } from '@/mobile/component/CustomForm';
import {
    encryptors,
    getRuntimeLocales,
    isIOS,
    langue,
    PRIVACYPOLICYVERSION,
    queryLoginConfig,
    USERAGREEMENTVERSION,
    voucherToToken,
} from '@/utils/common';
import { DEFAULTRETRIEVEPASSWORD } from '@/utils/constant';
import { useAsyncEffect, useLockFn, useUpdate } from '@streamax/hooks';
import i18n from 'i18next';
import request from '@/utils/request';
import { tt } from '@/utils/i18n';
import qs from 'querystring';
import { message } from '@streamax/poppy';
import md5 from 'js-md5';

let loginData = {};
const defaultLocales = require('../../../app.locale.json');
const encryptor = encryptors();
// 轮询验证码时间
const SLIDER_CODE_INTERVAL_TIME = 2 * 60 * 1000;
const countDownTotal = 121;
let countDownTimer = null;

export default () => {
    const [loginMode, setLoginMode] = useState<'phoneNumber' | 'account'>(
        'account',
    );
    const formRef = useRef<CustomFormRef>(null);
    const verifyRef = useRef<FormVerifyRef>(null);
    const [showTip, setShowTip] = useState(false);
    const [areaCodes, setAreaCodes] = useState<any[]>([]);
    const [config, setConfig] = useState<any>(null);
    const forceUpdate = useUpdate();
    const [errInfo, setErrInfo] = useState<string>();
    const [state, setState] = useState<boolean>(false);
    // 控制隐私协议展示、隐藏
    const [protocolInfo, setProtocolInfo] = useState({
        isShowProtocol: true,
        isCheckConfirm: true,
    });
    // 轮询请求验证码
    const codeVerifyInterval = useRef(null);

    const [languageTypes, setLanguageTypes] = useState<any>([]);

    const isCheckProtocol = protocolInfo.isCheckConfirm
        ? protocolInfo.isCheckConfirm && !protocolInfo.isShowProtocol
        : true;

    const [countdown, setCountDown] = useState(countDownTotal);

    const countdingDown = countdown < countDownTotal;
    useEffect(() => {
        getLoginConfig();
    }, []);

    useEffect(() => {
        if (errInfo && typeof errInfo.key === 'string') {
            showToast(errInfo.key);
        }
    }, [errInfo]);

    useAsyncEffect(async () => {
        let lng;
        if (config !== null) {
            lng = config?.defaultLanguage;
            window.localStorage.setItem('LOGIN_LANG', lng);
        }
        if (!lng) {
            lng = window.localStorage.getItem('LOGIN_LANG') || 'en_US';
        }
        formRef.current?.setFieldsValue({
            langue: lng,
        });
    }, [config]);

    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType || (!tenantId && tenantId != 0)) {
            return;
        }
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        setState(!state);
        forceUpdate();
        setDocumentTitle();
    };

    const setDocumentTitle = () => {
        document.title = i18n.t('@base:@action__login.title', '');
    };

    const getLoginConfig = async () => {
        let dataConfig: any = {};
        try {
            const data = await queryLoginConfig();
            // 处理隐私协议展示隐藏
            const privacyAgreementFlag = data.privacyAgreementFlag;
            // 只有设置了privacyAgreementFlag值为0，才隐藏不校验隐私协议，
            // 没有相关字段，或者为1，都需要校验
            setProtocolInfo({
                isShowProtocol: privacyAgreementFlag === 0 ? false : true,
                isCheckConfirm: data?.agreementCheckFlag === 0 ? false : true,
            });

            dataConfig = {
                ...data,
                privacyPolicyVersion:
                    data.privacyPolicyVersion || PRIVACYPOLICYVERSION,
                userAgreementVersion:
                    data.userAgreementVersion || USERAGREEMENTVERSION,
                retrievePassword:
                    data.retrievePassword || DEFAULTRETRIEVEPASSWORD,
            };
            if (data?.defaultLoginType && data?.defaultLoginType === 1) {
                setLoginMode('phoneNumber');
            }
            if (data?.languageList) {
                // 先将语言按照sort字段进行排序
                data.languageList = data.languageList
                    .sort((a: any, b: any) => {
                        return a.sort - b.sort;
                    })
                    .filter((i) => i.checked);
                setLanguageTypes(
                    data.languageList.map(
                        (item: { laguageName: string; sort: number }) => ({
                            label: langue[item.languageName],
                            value: item.languageName,
                        }),
                    ),
                );
            }
        } catch (e) {
            // return null;
        }
        setConfig(dataConfig);
    };

    useEffect(() => {
        if (loginMode === 'phoneNumber' && !areaCodes.length) {
            getAreaCodes();
        }

        // 清除定时器
        codeVerifyInterval.current && clearInterval(codeVerifyInterval.current);
        if (
            loginMode === 'account' &&
            config?.verificationCodeSwitch === true
        ) {
            // refreshCode();
        }
    }, [loginMode, config]);

    const onValuesChange = async (values: any, _: any) => {
        if (values.langue) {
            await i18nInit(values.langue, config?.tenantId);
            setDocumentTitle();
        }
    };

    const getAreaCodes = async () => {
        try {
            const data = await request.get(
                '/base-server-service/api/v1/areacode/query',
            );
            setAreaCodes(data);
        } catch (e) {
            // message.error(e);
        }
    };

    const action = async () => {
        const phoneNumber = formRef.current?.getFieldValue('phoneNumber');
        const areaCode = formRef.current?.getFieldValue('areaCode');
        const langue = formRef.current?.getFieldValue('langue');
        const pattern = areaCode == 86 ? /^\d{11}$/ : /^\d{1,20}$/;
        if (!phoneNumber) {
            showToast(tt('@base:@message__login.placeholder.phone').key);
        } else if (areaCode == 86 && !pattern.test(phoneNumber)) {
            showToast(tt('@base:@message__login.phone.rule').key);
        } else {
            handlePhoneVerfiyClick({ areaCode, phoneNumber, langue });
        }
    };

    const handlePhoneVerfiyClick = useLockFn(
        async ({ areaCode, phoneNumber, langue: lang }) => {
            if (countdingDown) return;
            try {
                await request.get('/base-server-service/api/v1/phone/verify', {
                    params: {
                        areaCode,
                        phoneNumber,
                        bizType: 0,
                    },
                    headers: {
                        _langtype:
                            lang ||
                            window.localStorage.getItem('LOGIN_LANG') ||
                            'zh_CN',
                    },
                });
                setCountDown((s) => s - 1);
                countDownTimer = setInterval(() => {
                    setCountDown((s) => {
                        if (s === 1) {
                            countDownTimer && clearInterval(countDownTimer);
                            return countDownTotal;
                        } else {
                            return s - 1;
                        }
                    });
                }, 1000);
            } catch (e) {
                message.error(e);
            }
        },
    );

    const getVerifyCodeText = () => {
        if (countdingDown) {
            return countdown;
        }
        return null;
    };

    const rules = {
        verifyCode: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__login.required.message.auth_code',
                            ),
                        );
                    }
                },
            },
            {
                type: ValidatorType.String,
                max: 6,
                message: tt(
                    '@base:@message__login.max.message.phone.auth_code',
                ),
            },
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!/^[\d]+$/.test(val)) {
                        callback(
                            tt(
                                '@base:@message__login.format.message.auth_code',
                            ),
                        );
                    }
                },
            },
        ],
        authCode: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__login.required.message.auth_code',
                            ),
                        );
                    }
                },
            },
            {
                type: ValidatorType.String,
                min: 4,
                message: tt('@base:@message__login.min.message.auth_code'),
            },
            {
                type: ValidatorType.String,
                max: 4,
                message: tt('@base:@message__login.max.message.auth_code'),
            },
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!/^[\d\w]+$/.test(val)) {
                        callback(
                            tt(
                                '@base:@message__login.format.message.auth_code',
                            ),
                        );
                    }
                },
            },
        ],
        account: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__login.required.message.username',
                            ),
                        );
                    } else if (!/^(?!.*(&__|\{\s*})).*$/g.test(val)) {
                        callback(
                            tt(
                                '@base:@message__login.username.chat.verification.message',
                            ),
                        );
                    }
                },
            },
        ],
        password: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    if (!val || val.trim() === '') {
                        callback(
                            tt(
                                '@base:@message__login.required.message.password',
                            ),
                        );
                    }
                },
            },
        ],
        phoneNumber: [
            {
                type: ValidatorType.Custom,
                validator: (val: any, callback: (message?: string) => void) => {
                    const areaCode = formRef.current?.getFieldValue('areaCode');
                    const pattern = areaCode == 86 ? /^\d{11}$/ : /^\d{1,20}$/;
                    if (!val || val.trim() === '') {
                        callback(tt('@base:@message__login.placeholder.phone'));
                    } else if (areaCode == 86 && !pattern.test(val)) {
                        callback(tt('@base:@message__login.phone.rule'));
                    }
                },
            },
        ],
    };

    const formItem = [
        loginMode === 'phoneNumber' && {
            component: (
                <FormSelect
                    dataSource={areaCodes.map((item: any) => ({
                        value: item.areaCode,
                        label:
                            '+' +
                            item.areaCode +
                            ' ' +
                            i18n.t(
                                `@i18n:@areaCode__${item.areaCode}`,
                                item.countryName,
                            ),
                    }))}
                />
            ),
            key: 'areaCode',
            field: 'areaCode',
            initialValue: 86,
        },
        loginMode === 'phoneNumber' && {
            field: 'phoneNumber',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__login.placeholder.phone',
                        '',
                    )}
                />
            ),
            key: 'phoneNumber',
            rules: rules.phoneNumber,
        },
        loginMode === 'account' && {
            field: 'account',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__login.placeholder.username',
                        '',
                    )}
                />
            ),
            key: 'account',
            rules: rules.account,
        },
        loginMode === 'account' && {
            field: 'password',
            component: (
                <Input
                    placeholder={i18n.t(
                        '@base:@message__login.placeholder.password',
                        '',
                    )}
                    type="password"
                />
            ),
            key: 'password',
            rules: rules.password,
        },
        {
            field: 'langue',
            component: (
                <FormSelect
                    dataSource={languageTypes}
                    onChange={(value) => {
                        window.localStorage.setItem('LOGIN_LANG', value);
                    }}
                />
            ),
            key: 'langue',
        },
        loginMode === 'phoneNumber' && {
            field: 'verifyCode',
            label: '',
            component: (
                <FormVerify
                    inputProps={{ maxLength: 6 }}
                    action={action}
                    mode={'phoneNumber'}
                    count={getVerifyCodeText()}
                />
            ),
            rules: rules.verifyCode,
            key: 'verifyCode',
        },
        loginMode === 'account' &&
            config?.verificationCodeSwitch && {
                field: 'authCode',
                label: '',
                component: (
                    <FormVerify
                        ref={verifyRef}
                        inputProps={{ maxLength: 4 }}
                        mode={'picture'}
                    />
                ),
                rules: rules.authCode,
                key: 'authCode',
            },
        protocolInfo.isShowProtocol && {
            field: 'agreed',
            label: '',
            component: (
                <FormAgreementCheckBox
                    needCheck={false}
                    popoverVisible={showTip}
                    config={config}
                />
            ),
            key: 'agreed',
            required: true,
        },
    ].filter(Boolean) as any;

    const onSubmit = async (values: any, result: any) => {
        const param = { ...values };
        let agreed = param.agreed;
        // if (isCheckProtocol) {
        //     // 若隐私协议不展示，则不校验
        //     agreed = true;
        // }
        agreed = true;
        if (!agreed) {
            setShowTip(true);
            return;
        }
        if (isCheckProtocol || agreed) {
            // 若隐私协议不展示，则不校验
            param.agreed = agreed;
            handleLogin(param);
            return;
        }
    };

    function getRedirectUrl(url: string) {
        const whiteList = ['localhost', '127.0.0.1'];
        let flag = false;
        whiteList.forEach((item) => {
            url.indexOf(item) > -1 && (flag = true);
        });
        if (flag) {
            return url;
        }
        if (url !== window.location.hostname) {
            return '/';
        }
        return url;
    }
    const history = useHistory();
    // 修改密码
    const updatePassword = () => {
        history.push({
            pathname: '/login/mobile/updatePassword',
            query: loginData,
        });
    };
    const errorReset = () => {
        loginMode === 'account' &&
            config.verificationCodeSwitch &&
            verifyRef.current?.refreshCode();
    };
    // 处理登录请求
    const handleLogin = useLockFn(async (param: any) => {
        const {
            privacyAgreementFlag,
            privacyPolicyVersion,
            userAgreementVersion,
        } = config;

        const { langue: lang, password, account } = param;
        // 终端类型：1：web端； 2：APP端； 3：API端； 不传则默认API登录。
        const clientType = 2;
        let loginParams;
        // 平板验证码登录
        if (loginMode === 'account') {
            loginParams = {
                ...param,
                uuid: verifyRef.current?.getUuid,
                password: password ? encryptor.encrypt(md5(password)) : '',
                clientType,
                loginType: config.verificationCodeSwitch !== false ? null : '2',
            };
        } else {
            loginParams = {
                ...param,
                type: 1,
                clientType,
            };
        }

        // 手机号登录
        if (loginMode === 'phoneNumber') {
            loginParams = {
                ...param,
                type: 1,
                clientType,
            };
        }

        if (privacyAgreementFlag) {
            loginParams = {
                ...loginParams,
                privacyPolicyVersion,
                userAgreementVersion,
            };
        }
        // 去除用户名的前后空格
        loginParams.account = account ? account.trim() : undefined;
        request
            .post(
                '/base-server-service/api/v1/user/newSecurityLogin',
                loginParams,
                {
                    noDataInterceptor: true,
                },
            )
            .then(async (rs: any) => {
                const { data } = rs;
                let resData;
                // 获取当前 URL 的 query 部分
                const search = window.location.search;
                // 使用 URLSearchParams 解析 query 参数
                const queryParams = new URLSearchParams(search);
                // 获取 redirect_url 参数
                const redirect_url = queryParams.get('redirect_url') || '';
                if (data.code === 200) {
                    resData = data.data;
                    const { voucher } = resData;
                    window.localStorage.setItem('AUTH_VOUCHER', voucher);
                    window.localStorage.setItem('LANG', lang);
                    if (redirect_url) {
                        // 解决安全问题，如果当前重定向的hostname与登录的hostname不同，则默认跳转到首页
                        // if (urlInfo.hostname !== window.location.hostname) {
                        //     window.location.replace('/');
                        //     return;
                        // }
                        const urlInfo = new URL(redirect_url);
                        const url = getRedirectUrl(urlInfo.hostname);
                        if (url === '/') {
                            window.location.replace('/');
                            return;
                        }
                        const urlQuery = qs.parse(
                            urlInfo.search.replace('?', ''),
                        );

                        urlQuery.auth_voucher = voucher;
                        urlQuery.lang = lang;
                        window.location.replace(
                            `${urlInfo.origin}${
                                urlInfo.pathname
                            }?${qs.stringify(urlQuery)}${urlInfo.hash}`,
                        );
                        return;
                    } else {
                        // 没有redirect_url，则拼接voucher到url
                        window.location.replace(
                            `${window.location.origin}?auth_voucher=${voucher}`,
                        );
                    }
                } else if (data.code === *********) {
                    const {
                        voucher,
                        emailNumber,
                        phoneNumber,
                        areaCode,
                        account,
                        userId,
                        verificationMode,
                        defaultMode,
                        tenantId,
                    } = data.data;
                    // 需要进行二次验证
                    sessionStorage.setItem('second-validate-voucher', voucher);
                    const params = {
                        redirect_url: redirect_url,
                        mailNumber: emailNumber,
                        phoneNumber,
                        areaCode,
                        account,
                        userId,
                        verificationMode,
                        defaultMode: defaultMode + '',
                        tenantId,
                    };
                    history.push({
                        pathname: '/login/mobile/secondValidate',
                        query: params,
                    });
                } else if (data.code === 3118004) {
                    // 状态吗  3118004  密码过期或者第一次登陆需要修改密码
                    // 第一次登录修改密码或者密码过期 需要登录获取token
                    resData = data.data;
                    // 信令转为token 出错可以被捕获
                    const {
                        headers: { _token },
                    } = await voucherToToken({
                        voucher: resData.voucher,
                    });
                    window.localStorage.setItem(
                        'UPDATE_PASSWORD_TOKEN',
                        _token as string,
                    );
                    loginData = {
                        account: resData.account,
                        userId: resData.userId,
                        redirect_url: redirect_url,
                    };
                    window.modalInstance = Dialog.confirm({
                        title: i18n.t(
                            '@base:@message__update_password.messagebox.title',
                            '',
                        ),
                        contentAlign: 'center',
                        children: i18n.t(data.langKey),
                        okText: i18n.t(
                            '@base:@action__login.active.modal.ok',
                            '',
                        ),
                        cancelText: i18n.t(
                            '@base:@action__login.active.modal.cancel',
                            '',
                        ),
                        onOk: updatePassword,
                    });
                } else {
                    setErrInfo(
                        // @ts-ignore
                        tt(data?.langKey, '', {
                            count: (data['errorVar'] || [])[0],
                        }),
                    );
                    // // 关闭并重置滑块验证码位置
                    errorReset();
                }
            })
            .catch((err: any) => {
                setErrInfo(err.toString());
                errorReset();
            });
    });

    const showNetRecordItem = (type: string) => {
        if (type === 'police') {
            return config?.netFilingEnable && config.policeFilingEnable;
        } else {
            return config?.netFilingEnable && config.icpFilingEnable;
        }
    };

    const notShowNetRecord =
        [0, null].includes(config?.netFilingEnable) ||
        ([0, null].includes(config?.policeFilingEnable) &&
            [0, null].includes(config?.icpFilingEnable));

    const renderNetRecord = () => {
        return !notShowNetRecord ? (
            <div className={'copyright-container'}>
                <div className={'copyright-text'}>
                    {showNetRecordItem('police') ? (
                        <a
                            href={`https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=${config.policeFilingNumber}`}
                            className="net-record-first-item"
                            target="_blank"
                        >
                            {config.policeFilingContent}
                        </a>
                    ) : null}
                </div>
                <div className={'copyright-text'}>
                    {showNetRecordItem('icp') ? (
                        <a
                            href="https://beian.miit.gov.cn"
                            target="_blank"
                            className="net-record-first-item"
                        >
                            {config.icpFilingContent}
                        </a>
                    ) : null}
                </div>
            </div>
        ) : null;
    };
    return (
        <div
            id={'mobile-login-page'}
            onClick={() => {
                showTip && setShowTip(false);
            }}
        >
            <div className="mobile-login-text">
                {loginMode === 'phoneNumber'
                    ? i18n.t('@base:@message__login.type.phone', '')
                    : i18n.t('@base:@message__login.type.account', '')}
            </div>
            <CustomForm
                className={'mobile-login-form'}
                ref={formRef}
                formItem={formItem}
                submitText={i18n.t('@base:@action__login.button', '')}
                formProps={{
                    onSubmit,
                    initialValues: { agreed: false },
                    onValuesChange,
                }}
            />
            {config?.retrievePassword === 1 && (
                <div className={'mobile-link forget-password'}>
                    <span
                        onClick={() => {
                            history.push({
                                pathname: '/login/mobile/findPassword',
                            });
                        }}
                    >
                        {i18n.t('@base:@action__forget_password.title', '')}
                    </span>
                </div>
            )}
            {config?.phoneFlag ? (
                <div className={'mobile-link switch-login-way-container'}>
                    {loginMode === 'phoneNumber' ? (
                        <div
                            className={'switch-login-way'}
                            onClick={() => {
                                setLoginMode('account');
                            }}
                        >
                            <img
                                src={userIcon}
                                alt=""
                                className="mobile-user-img"
                            />
                            <span>
                                {i18n.t(
                                    '@base:@message__login.type.account',
                                    '',
                                )}
                            </span>
                        </div>
                    ) : (
                        <div
                            className={'switch-login-way'}
                            onClick={() => {
                                setLoginMode('phoneNumber');
                            }}
                        >
                            <img
                                src={mobileIcon}
                                alt=""
                                className="mobile-user-img"
                            />
                            <span>
                                {i18n.t('@base:@message__login.type.phone', '')}
                            </span>
                        </div>
                    )}
                </div>
            ) : null}
            <div className="mobile-error-info">{errInfo}</div>
            {renderNetRecord()}
        </div>
    );
};

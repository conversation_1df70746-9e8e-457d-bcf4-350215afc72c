import { history } from 'umi';
import CustomForm, { CustomFormRef } from '@/mobile/component/CustomForm';
import { getAccountFormItem } from '@/mobile/component/account';
import {
    getConfirmNewPasswordFormItem,
    getNewPasswordFormItem,
    getOldPasswordFormItem,
} from '@/mobile/component/password';
import usePasswordConfig from '@/hooks/usePasswordConfig';
import { useEffect, useRef, useState } from 'react';
import request from '@/utils/request';
import { encryptors } from '@/utils/common';
import md5 from 'js-md5';
import { showToast } from '@/mobile/util';
import './index.less';
import { useAsyncEffect } from '@streamax/hooks';
import i18n from 'i18next';

// RSA 2048 加密
const encryptor = encryptors();
export default () => {
    const { pwdRules } = usePasswordConfig(true);
    const formRef = useRef<CustomFormRef>(null);
    const [loading, setLoading] = useState(false);
    const timeOutRef = useRef();
    const formItem = [
        getAccountFormItem({ disabled: true }),
        getOldPasswordFormItem(),
        getNewPasswordFormItem(formRef.current, pwdRules),
        getConfirmNewPasswordFormItem(formRef.current, pwdRules),
    ];
    useEffect(() => {
        const form = formRef.current as CustomFormRef;
        form.resetFields();
        form.setFieldsValue({
            account: history.location.query.account,
        });
    }, []);

    // 返回登录
    const logout = async () => {
        clearTimeout(timeOutRef.current);
        const loginUrl = location.origin + '/login/mobile/login';
        const redirect_url =
            '?redirect_url=' + history.location.query.redirect_url;
        const callbackUrl = `${loginUrl}${
            history.location.query.redirect_url ? redirect_url : ''
        }`;
        window.location.href = callbackUrl;
    };
    // 更新密码
    const handleUpdatePassword = async () => {
        const form = formRef.current as CustomFormRef;
        form.validateFields().then(async () => {
            const values = form.getFieldsValue();
            setLoading(true);
            // @ts-ignore
            request({
                method: 'post',
                url: '/base-server-service/api/v1/user/pwd',
                noDataInterceptor: true,
                data: {
                    newPwd: encryptor.encrypt(md5(values.newPassword)),
                    oldPwd: encryptor.encrypt(md5(values.oldPassword)),
                    userId: history.location.query.userId,
                },
                headers: {
                    _token: window.localStorage.getItem(
                        'UPDATE_PASSWORD_TOKEN',
                    ),
                },
            })
                .then((rs: any) => {
                    const { data, config } = rs;
                    if (data.code === 200 || data.code === '200') {
                        setLoading(false);
                        showToast(
                            i18n.t(
                                '@base:@message__update_password.message.success.tip',
                                {
                                    count: 3,
                                },
                            ),
                        );
                        timeOutRef.current = setTimeout(logout, 3000);
                    } else if (
                        data.code == 1403 ||
                        data.code == 1407 ||
                        data.code == 1458
                    ) {
                        setLoading(false);
                        showToast(
                            i18n.t(data.langKey, '', {
                                count: (data['errorVar'] || [])[0],
                            }),
                        );
                    } else {
                        timeOutRef.current = setTimeout(logout, 3000);
                        setLoading(false);
                        showToast(
                            i18n.t(data.langKey, '', {
                                count: (data['errorVar'] || [])[0],
                            }),
                        );
                    }
                })
                .catch((err: any) => {
                    setLoading(false);
                    showToast(err);
                });
        });
    };
    const onValuesChange = (values: any) => {
        const inputKey = Object.keys(values)[0];
        const form = formRef.current as CustomFormRef;
        if (!form) return;
        const { confirmPassword, newPassword } = form.getFieldsValue();
        if (inputKey === 'newPassword' && confirmPassword) {
            form.validateFields(['confirmPassword']);
        } else if (inputKey === 'oldPassword' && newPassword) {
            form.validateFields(['newPassword']);
        }
    };
    useAsyncEffect(async () => {
        document.title = i18n.t('@base:@action__update_password.title');
    }, []);
    return (
        <div id="mobile-update-password-page">
            <div className="mobile-update-password-text">
                {i18n.t('@base:@action__update_password.title')}
            </div>
            <div className="mobile-update-password-describe">
                <span>{i18n.t('@base:@message__update_password.tips')}</span>
            </div>
            <CustomForm
                className={'mobile-login-form'}
                ref={formRef}
                formItem={formItem}
                submitText={i18n.t(
                    '@base:@action__update_password.confirm',
                    '',
                )}
                submitLoading={loading}
                formProps={{
                    onSubmit: handleUpdatePassword,
                    onValuesChange,
                }}
            />
        </div>
    );
};

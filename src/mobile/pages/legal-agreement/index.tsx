import { useState, useRef, useEffect } from 'react';
import { history } from 'umi';
import { message, Select } from '@streamax/poppy';
// import { allLanguages, languageProps } from './languages';
import { getRuntimeLocales, queryLoginConfig } from '@/utils/common';
import i18n from 'i18next';
import { useAsyncEffect } from '@streamax/hooks';
import { PDFDocument, degrees } from 'pdf-lib';
import ReactHtmlParser from 'react-html-parser'; // 引入解析库
import backIcon from '@/assets/mobile/icon-back.svg';
import * as PDFJS from 'pdfjs-dist/build/pdf';
import PDFJSWorker from 'pdfjs-dist/build/pdf.worker.entry.js';

import './index.less';
PDFJS.GlobalWorkerOptions.workerSrc = PDFJSWorker;

const defaultLocales = require('../../../app.locale.json');
const availableLanguage = [
    'zh_CN',
    'en_US',
    'fr_FR',
    'pt_BR',
    'es_ES',
    // 'vi_VN',
]; //支持国际化的语言，其他默认英文
// import './index.less';
import { agreementMap } from '@/pc/pages/login';
import {
    allLanguages,
    languageProps,
} from '@/pc/pages/legalAgreement/languages';
import {tt} from "@/utils/i18n";
export enum LegalType {
    agree = 'agree', //用户协议
    secret = 'secret', //隐私声明
}
type LanguageType = keyof languageProps;

export default () => {
    const [language, setLanguage] = useState<string>('en_US');
    const [statement, setStatement] = useState<string>(
        allLanguages['zh_CN_statement'],
    );
    const [state, setState] = useState(false);
    const [userAgreement, setUserAgreement] = useState<string>(
        allLanguages['zh_CN_user_greement'],
    );
    const [config, setConfig] = useState<any>();
    const [showDefault, setShowDefault] = useState<boolean>(false);
    const [fileLinks, setFileLinks] = useState();
    const asyncOperationRef = useRef<(() => Promise<void>)[]>([]);
    const { type, initFileUrl, custom }: any = history.location.query;

    useEffect(() => {
        if (custom && Number(custom) === 1) {
            setShowDefault(false);
        } else {
            setShowDefault(true);
        }
    }, [custom]);

    const setDocumentTitle = () => {
        let pageTitle = '';
        if (type === 'agree') {
            pageTitle = i18n.t(
                '@base:@name__document_title_password.title.legal',
            );
        } else {
            pageTitle = i18n.t(
                '@base:@name__document_title_password.title.greement',
            );
        }
        document.title = pageTitle;
    };
    const i18nInit = async (langType: string) => {
        if (!langType) return;
        // @ts-ignore
        const locales = await getRuntimeLocales(langType);
        // @ts-ignore
        window.I18nInit(langType, { ...defaultLocales, ...locales });
        setState(!state);
        setDocumentTitle();
        asyncOperationRef.current.shift();
        if (asyncOperationRef.current.length > 0) {
            await asyncOperationRef.current[0]();
        }
    };
    useAsyncEffect(async () => {
        const defalutLang =
            window.localStorage.getItem('LOGIN_LANG') || 'en_US';
        if (custom == 1 || availableLanguage.includes(defalutLang)) {
            setLanguage(defalutLang);
        }
        const data = await queryLoginConfig();
        setConfig(data);
    }, []);

    useAsyncEffect(async () => {
        // 根据所选语言设置语言类型
        const languaVariableStatement: string = language + '_statement';
        const languaVariableUser_greement: string = language + '_user_greement';
        setStatement(allLanguages[languaVariableStatement as LanguageType]);
        setUserAgreement(
            allLanguages[languaVariableUser_greement as LanguageType],
        );
        const asyncOperation = async () => {
            await i18nInit(language);
        };
        asyncOperationRef.current.push(asyncOperation);
        if (asyncOperationRef.current.length === 1) {
            await asyncOperationRef.current[0]();
        }
    }, [language]);

    useEffect(() => {
        if (config) {
            const customAgreementPolicy = config[agreementMap[type]];
            if (customAgreementPolicy) {
                setFileLinks(customAgreementPolicy.fileList);
                custom && Number(custom) === 1 && getPdf(initFileUrl);
            }
        }
    }, [config]);

    const RenderedContent = ({ content }: { content: string }) => {
        const parsedContent = ReactHtmlParser(content);
        return <div>{parsedContent}</div>;
    };
    const renderDefault =
        type === LegalType.agree ? (
            <div className="mobile-login-agreement-container-item">
                <RenderedContent content={userAgreement} />
            </div>
        ) : (
            <div className="mobile-login-agreement-container-item">
                <RenderedContent content={statement} />
            </div>
        );
    const renderCustom = <canvas id="pdf-canvas" />;
    const getPdf = async (url: string) => {
        const container = document.getElementsByClassName(
            'mobile-login-agreement-container',
        )[0];
        container.classList.add('mobile-login-agreement-container-pdf');
        const fragment = document.createDocumentFragment();
        const CMapReaderFactory = {
            url,
        };
        const loadingTask = PDFJS.getDocument(CMapReaderFactory);
        const pdfInfo = await loadingTask.promise; // 获取pdf实例
        const pdfInstance = pdfInfo; // 设置pdf实例
        const pdfPages = pdfInfo.numPages; // 获取解析pdf的页数
        const createCanvas = async (currentPage: number) => {
            const canvas = document.createElement('canvas'); // 创建canvas标签
            const page = await pdfInstance.getPage(currentPage); // 根据pdf实例和解析出pdf的页数得出一个page信息
            const h = 320;
            page._pageInfo.view[1] = page._pageInfo.view[1] + h;
            const viewport = await page.getViewport({
                scale: 2,
                offsetY: -143,
            }); // 获取page视口信息
            viewport.height = viewport.height + h + 16;
            const context = canvas.getContext('2d'); // 返回一个用于在画布上绘图的环境
            canvas.width = viewport.width; // 设置画布宽度
            canvas.height = viewport.height; // 设置画布高度
            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            };

            const renderTask = await page.render(renderContext);
            await renderTask.promise;
            fragment.appendChild(canvas);
            if (currentPage === pdfPages) {
                removeChildren(container);
                container.appendChild(fragment);
            }
        };
        const renderPage = async (page: number) => {
            await createCanvas(page);
        };

        async function asyncRenderPages() {
            const pages = new Array(pdfPages);
            for (let i = 0; i < pdfPages; i++) {
                pages[i] = i + 1;
            }

            for (const item of pages) {
                try {
                    await renderPage(item);
                } catch (error) {
                    console.error('Error:', error);
                }
            }
        }

        asyncRenderPages();
    };
    const removeChildren = (parentDom: Element) => {
        while (parentDom.firstChild) {
            parentDom.removeChild(parentDom.firstChild);
        }
    };

    return (
        <div id="mobile-login-agreement">
            <div className={'mobile-login-agreement-navbar'}>
                <img
                    className={'mobile-login-agreement-back'}
                    src={backIcon}
                    alt=""
                    onClick={() => {
                        history.goBack();
                    }}
                />
                <span className={'mobile-login-agreement-title'}>
                    {tt(
                        '@base:@message_login.legalAgreement.mobile.title',
                    )}
                </span>
            </div>
            <div className="mobile-login-agreement-container">
                {showDefault ? renderDefault : renderCustom}
                {/*{showDefault ? renderDefault : renderCustom}*/}
                {/* { renderDefault }*/}
            </div>
        </div>
    );
};

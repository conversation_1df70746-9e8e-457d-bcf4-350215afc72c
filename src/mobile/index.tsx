import { isMobile } from '@/utils/common';
import { history } from 'umi';
import { queryLoginConfig, getRuntimeLocales } from '@/utils/common';
import './style/index.less';
/**

 *<AUTHOR>

 *@date 2024/11/14 10:59:56

 *@description: add a description

 **/
import setRootPixel from '@arco-design/mobile-react/tools/flexible';
import './style/index.less';
import VConsole from 'vconsole';
import { useAsyncEffect, useConstructor, useUpdate } from '@streamax/hooks';
const defaultLocales = require('../app.locale.json');
export default (props: { children: any }) => {
    const deviceType = isMobile();
    const update = useUpdate();
    if (!deviceType) {
        const queryParams = history.location.search;
        history.replace({
            pathname: '/login/pc/login',
            search: queryParams,
        });
    }
    setRootPixel();
    // useConstructor(() => {
    //     new VConsole();
    // });

    // 获取国际化词条
    const i18nInit = async (langType: string, tenantId: string) => {
        if (!langType) return;
        // @ts-ignore
        const locales = await getRuntimeLocales(langType, tenantId);
        // @ts-ignore
        await window.I18nInit(langType, { ...defaultLocales, ...locales });
        update();
    };
    // 获取登录策略
    const getLoginConfig = async () => {
        let dataConfig: any = {};
        dataConfig = await queryLoginConfig();
        return dataConfig;
    };
    useAsyncEffect(async () => {
        const lng = window.localStorage.getItem('LOGIN_LANG') || 'en_US';
        const loginConfig = await getLoginConfig();
        await i18nInit(lng as string, loginConfig?.tenantId);
    }, []);
    if (
        !window.langueCache[
            window.localStorage.getItem('LOGIN_LANG') || 'en_US'
        ] ||
        // !window.i18nLoaded ||
        window.langueCache[
            window.localStorage.getItem('LOGIN_LANG') || 'en_US'
        ] === 'loading'
    ) {
        return null;
    }
    return <>{props.children}</>;
};

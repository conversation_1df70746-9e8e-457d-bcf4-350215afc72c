import { Button, Form } from '@arco-design/mobile-react';
import { forwardRef, useImperativeHandle } from 'react';
import { useForm } from '@arco-design/mobile-react/esnext/form';
import {
    FormProps,
    IFieldError,
} from '@arco-design/mobile-react/cjs/form/type';
import { showToast } from '@/mobile/util';
import './index.less';

/**

 *<AUTHOR>

 *@date 2024/11/17 11:17:23

 *@description: add a description

 **/
export interface FormItem {
    component: any;
    field: string;
    key: string;
    label?: string;
    required?: boolean;
    rules?: any[];
    noStyle?: boolean;
}
interface CustomFormProps {
    className?: string;
    formItem: FormItem[];
    submitText: string | boolean;
    submitLoading?: boolean;
    formProps: {
        onSubmit?: (values: any, result: any) => void;
        onSubmitFailed?: (
            values: any,
            errorInfo: IFieldError[] | Error,
        ) => void;
        initialValues?: {};
        onValuesChange?: (changedValues: any, values: any) => void;
    };
}

export interface CustomFormRef {
    getFieldValue: (name: string) => any;
    getFieldsValue: () => Record<string, any>;
    setFieldsValue: (value: Record<string, any>) => void;
    validateFields: () => any;
    resetFields: () => void;
}
export default forwardRef<CustomFormRef, CustomFormProps>(
    (props: CustomFormProps, ref) => {
        const { formItem, submitText, formProps, className, submitLoading } =
            props;
        const [form] = useForm();

        // 将 formRef 暴露给父组件
        useImperativeHandle(ref, () => ({
            getFieldValue: form.getFieldValue,
            getFieldsValue: form.getFieldsValue,
            setFieldsValue: form.setFieldsValue,
            validateFields: form.validateFields,
            resetFields: form.resetFields,
        }));

        const onSubmitFailed = async (
            _: any,
            errorInfo: IFieldError[] | Error,
        ) => {
            if (Array.isArray(errorInfo) && errorInfo[0]?.errors) {
                const errorText =
                    errorInfo[0]?.errors[0]?.key || errorInfo[0]?.errors[0];
                showToast(errorText as string);
            }
        };

        return (
            <div id="custom-form">
                <Form
                    form={form}
                    className={`mobile-custom-form ${className}`}
                    layout={'inline'}
                    onSubmitFailed={onSubmitFailed}
                    {...formProps}
                >
                    {formItem.map((item: any) => {
                        const { noStyle, ...itemProps } = item;
                        if (noStyle) {
                            return (
                                <Form.Item
                                    key={item.key}
                                    {...itemProps}
                                    style={{ margin: 0, padding: 0 }}
                                    className="mobile-custom-form-item-no-style"
                                >
                                    {item.component}
                                </Form.Item>
                            );
                        }

                        return (
                            <Form.Item
                                label=""
                                className={`mobile-custom-form-item ${
                                    item.disabled ? 'form-item-disabled' : ''
                                }`}
                                {...itemProps}
                            >
                                {item.component}
                            </Form.Item>
                        );
                    })}
                    {submitText === false ? null : <Button
                        loading={submitLoading}
                        className={'mobile-custom-submit'}
                        needActive
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        {submitText}
                    </Button>}
                </Form>
            </div>
        );
    },
);

import { Checkbox, Popover } from '@arco-design/mobile-react';
import {
    IconSquareChecked,
    IconSquareDisabled,
    IconSquareUnchecked,
} from '@arco-design/mobile-react/esnext/icon';
import './index.less';
import i18n from 'i18next';
import { tt } from '@/utils/i18n';
import { LegalType } from '@/pc/pages/legalAgreement';
import { CUSTOM_AGREEMENT_TYPE } from '@/utils/constant';
import { agreementMap } from '@/pc/pages/login';
import { useHistory } from 'umi';

/**

 *<AUTHOR>

 *@date 2024/11/15 19:23:53

 *@description: add a description

 **/
interface FormSelectProps {
    value?: any;
    onChange?: (value: any) => any;
    needCheck: boolean;
    popoverVisible: boolean;
    config: any;
}

export default (props: FormSelectProps) => {
    const { onChange, value, needCheck, popoverVisible, config } = props;
    const history = useHistory();
    const squareIcon = {
        normal: <IconSquareUnchecked />,
        active: <IconSquareChecked />,
        disabled: <IconSquareDisabled />,
        activeDisabled: <IconSquareChecked />,
    };

    // 隐私声明
    const readAgreement = (type: LegalType) => {
        const openCustom = config[agreementMap[type]]?.switchFlag === 1;
        const fileType = config[agreementMap[type]]?.type; // 1 为文件(fileList)  2 为链接(fileLink)
        if (openCustom && fileType === CUSTOM_AGREEMENT_TYPE.LINK) {
            window.open(config[agreementMap[type]].fileLink, '_blank');
            return;
        }
        const languageCode = localStorage.getItem('LOGIN_LANG');
        const { fileList } = config[agreementMap[type]] || {};
        const pdfUrl = (fileList || []).find(
            (item) => item.langType === languageCode,
        )?.fileUrl;

        history.push({
            pathname: '/login/mobile/legalAgreement',
            // @ts-ignore
            query: {
                type,
                custom: openCustom ? 1 : 0,
                initFileUrl: pdfUrl ?? '',
            },
        });
    };

    return (
        <div id={'form-agreement-check-box'}>
            {needCheck && (
                <Popover
                    content={tt(
                        '@base:@message__update_password.message.legal_greement',
                    )}
                    direction="bottomLeft"
                    visible={popoverVisible && !value}
                    horizontalOffset={0}
                >
                    <Checkbox
                        value={''}
                        icons={squareIcon}
                        onChange={onChange}
                        checked={value}
                    />
                </Popover>
            )}
            <div className={'check-text'}>
                {needCheck
                    ? i18n.t(
                          '@base:@message__update_password.title.legal_greement',
                          '',
                      )
                    : i18n.t(
                          '@base:@message__update_password.title.no_legal_greement',
                          '',
                      )}
                &nbsp;
                <span
                    className={'mobile-link'}
                    onClick={(e) => {
                        readAgreement(LegalType.agree);
                        e.stopPropagation();
                    }}
                >
                    {i18n.t('@base:@message__update_password.title.legal', '')}
                    &nbsp;
                </span>
                {i18n.t('@base:@message__update_password.title.and', '')} &nbsp;
                <span
                    className={'mobile-link'}
                    onClick={(e) => {
                        readAgreement(LegalType.secret);
                        e.stopPropagation();
                    }}
                >
                    {i18n.t(
                        '@base:@message__update_password.title.greement',
                        '',
                    )}
                </span>
            </div>
        </div>
    );
};

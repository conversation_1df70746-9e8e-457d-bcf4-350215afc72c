import i18n from 'i18next';
import { Input } from '@arco-design/mobile-react';
import { ValidatorType } from '@arco-design/mobile-utils';
import { Rule } from '@streamax/poppy/lib/form';
import { getRequiredRule } from '../form-rule';

export const getOldPasswordRule = (extendRules: Rule[] = []) => [
    getRequiredRule(
        i18n.t('@base:@message__update_password.required.message.old_password'),
    ),
    ...extendRules,
];
export const getOldPasswordFormItem = (extendRules: Rule[] = []) => ({
    field: 'oldPassword',
    initialValue: '',
    component: (
        <Input
            type="password"
            placeholder={i18n.t(
                '@base:@message__update_password.placeholder.old_password',
                '',
            )}
            maxLength={20}
        />
    ),
    key: 'oldPassword',
    rules: getOldPasswordRule(extendRules),
});

const getValidatorSameToOldpassword = (fromInstance: any) => ({
    type: ValidatorType.Custom,
    validator(value: string, callback: any) {
        if (
            !value ||
            !fromInstance.getFieldValue('oldPassword') ||
            fromInstance.getFieldValue('oldPassword') !== value
        ) {
            return callback();
        }
        return callback(
            i18n.t(
                '@base:@message__update_password.message.new_password.sameto.old_password',
            ),
        );
    },
});
export const getNewPasswordRule = (
    fromInstance: any,
    extendRules: Rule[] = [],
) => [
    getRequiredRule(
        i18n.t('@base:@message__update_password.required.message.new_password'),
    ),
    getValidatorSameToOldpassword(fromInstance),
    ...extendRules,
];
export const getNewPasswordFormItem = (
    fromInstance: any,
    extendRules: Rule[] = [],
) => ({
    field: 'newPassword',
    initialValue: '',
    component: (
        <Input
            placeholder={i18n.t(
                '@base:@message__update_password.placeholder.new_password',
                '',
            )}
            type="password"
            maxLength={20}
        />
    ),
    key: 'newPassword',
    rules: getNewPasswordRule(fromInstance, extendRules),
});

const validatorNewPassword = (fromInstance: any) => ({
    type: ValidatorType.Custom,
    validator(value: string, callback: any) {
        if (!value || fromInstance.getFieldValue('newPassword') === value) {
            return callback();
        }
        return callback(
            i18n.t('@base:@message__update_password.message.same_password'),
        );
    },
});
export const getConfirmNewPasswordRule = (
    fromInstance: any,
    extendRules: Rule[] = [],
) => [
    getRequiredRule(
        i18n.t(
            '@base:@message__update_password.required.message.confirm_password',
        ),
    ),
    validatorNewPassword(fromInstance),
    ...extendRules,
];
export const getConfirmNewPasswordFormItem = (
    fromInstance: any,
    extendRules: Rule[] = [],
) => ({
    field: 'confirmPassword',
    initialValue: '',
    component: (
        <Input
            type="password"
            placeholder={i18n.t(
                '@base:@message__update_password.placeholder.confirm_password',
                '',
            )}
            maxLength={20}
        />
    ),
    key: 'confirmPassword',
    rules: getConfirmNewPasswordRule(fromInstance, extendRules),
});

/*
 * @LastEditTime: 2025-05-20 19:28:25
 */
import i18n from 'i18next';
import { Input } from '@arco-design/mobile-react';
import { ValidatorType } from '@arco-design/mobile-utils';

export const getAccountRule = () => [
    {
        type: ValidatorType.Custom,
        validator: (val: any, callback: (message?: string) => void) => {
            if (!val) {
                callback(
                    i18n.t(
                        '@base:@message__login.required.message.username',
                        '',
                    ),
                );
            } else if (!/^(?!.*(&__|\{\s*})).*$/g.test(val)) {
                callback(
                    i18n.t(
                        '@base:@message__login.username.chat.verification.message',
                        '',
                    ),
                );
            }
        },
    },
];
interface AccountProps {
    disabled?: boolean;
}
export const getAccountFormItem = (accountProps: AccountProps) => ({
    field: 'account',
    initialValue: '',
    component: (
        <Input
            placeholder={i18n.t(
                '@base:@message__login.placeholder.username',
                '',
            )}
        />
    ),
    key: 'account',
    rules: getAccountRule(),
    ...accountProps,
});

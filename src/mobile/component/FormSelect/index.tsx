import downArrowIcon from '@/assets/mobile/icon-down-line.svg';
import { Cell, Picker } from '@arco-design/mobile-react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
import {tt} from "@/utils/i18n";

/**

 *<AUTHOR>

 *@date 2024/11/15 19:23:53

 *@description: add a description

 **/
interface FormSelectProps {
    value?: any;
    onChange?: (value: any) => any;
    dataSource: any;
    placeholder?: string;
}
export interface FormSelectRef {}
export default forwardRef<FormSelectRef, FormSelectProps>(
    (props: FormSelectProps, ref) => {
        const { dataSource, onChange, value, placeholder } = props;
        const [visible, setVisible] = useState(false);
        const [label, setLabel] = useState('');

        // 将 formRef 暴露给父组件
        useImperativeHandle(ref, () => ({}));

        useEffect(() => {
            if (value && dataSource) {
                const target = dataSource.find((i: any) => i.value === value);
                setLabel(target?.label || '');
            }
        }, [value, dataSource]);

        return (
            <div id={'form-select'}>
                <Cell
                    className={`mobile-select-cell ${
                        label ? '' : 'show-placeholder'
                    }`}
                    label={label || placeholder}
                    showArrow
                    bordered={false}
                    arrow={
                        <img
                            src={downArrowIcon}
                            alt=""
                            className="mobile-select-img"
                        />
                    }
                    onClick={() => {
                        setVisible(true);
                    }}
                />
                <Picker
                    visible={visible}
                    cascade={false}
                    data={[dataSource]}
                    maskClosable={true}
                    onHide={() => {
                        setVisible(false);
                    }}
                    onOk={(value, a) => {
                        onChange?.(value[0]);
                    }}
                    onOpen={() => {}}
                    value={[value]}
                    okText={tt('@base:@action__login.active.modal.ok')}
                    dismissText={tt('@base:@action__login.active.modal.cancel')}
                />
            </div>
        );
    },
);

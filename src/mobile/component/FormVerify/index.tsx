import { Button, Input } from '@arco-design/mobile-react';
import sendIcon from '@/assets/mobile/icon-release.svg';
import './index.less';
import i18n from 'i18next';
import request from '@/utils/request';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

/**

 *<AUTHOR>

 *@date 2024/11/15 19:23:53

 *@description: add a description

 **/

export interface FormVerifyRef {
    getUuid: string;
    refreshCode: () => void;
}

interface FormVerifyProps {
    value?: any;
    onChange?: (value: any) => any;
    action?: () => any;
    mode: 'phoneNumber' | 'picture' | 'OTPCode';
    inputProps?: {};
    count?: string;
}

export default forwardRef<FormVerifyRef, FormVerifyProps>(
    (props: FormVerifyProps, ref) => {
        const { onChange, value, action, mode, inputProps, count } = props;
        const [authParams, setAuthParams] = useState({
            uuid: '',
        });
        const [codeSrc, setCodeSrc] = useState('');

        // 将 formRef 暴露给父组件
        useImperativeHandle(ref, () => ({
            getUuid: authParams.uuid,
            refreshCode: refreshCode,
        }));

        const handleOnChange = (e: any, value: any) => {
            onChange?.(value);
        };

        const getSrc = () => {
            return new Promise((resolve) => {
                request
                    .get('/base-server-service/api/v1/authcode/newObtain')
                    .then((data: any) => {
                        resolve(data);
                    });
            });
        };

        const refreshCode = async () => {
            const res: any = await getSrc();
            setAuthParams({
                uuid: res.uuid,
            });
            setCodeSrc(`data:image/gif;base64,${res.authCodePic}`);
        };

        useEffect(() => {
            if (mode === 'picture') {
                refreshCode();
            }
        }, []);

        const getVerifyBtn = (mode: 'phoneNumber' | 'picture' | 'OTPCode')=>{
            switch (mode){
                case 'phoneNumber':
                    return (
                        <Button
                            className={`mobile-login-send ${
                                count ? 'processing' : ''
                            }`}
                            onClick={action}
                        >
                            {count ? (
                                <span>{count}</span>
                            ) : (
                                <img
                                    src={sendIcon}
                                    alt=""
                                    className="mobile-send-img"
                                />
                            )}
                        </Button>
                    );
                case 'picture':
                    return (
                        <img
                            className={'mobile-login-verify-pic'}
                            onClick={() => {
                                refreshCode();
                            }}
                            src={codeSrc}
                        />
                    );
                default: return null;
            }
        };

        return (
            <>
                <div id="form-phone-verify-code">
                    <Input
                        className={'input-auto-width'}
                        value={value}
                        placeholder={i18n.t(
                            '@base:@message__login.placeholder.auth_code',
                            '',
                        )}
                        onChange={handleOnChange}
                        {...(inputProps || {})}
                    />
                    {getVerifyBtn(mode)}
                </div>
            </>
        );
    },
);

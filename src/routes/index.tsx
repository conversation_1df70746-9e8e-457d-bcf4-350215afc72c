export default [
    {
        path: '/login',
        component: '@/baseLayout',
        exact: true,
    },
    {
        path: '/login/pc',
        component: '@/pc',
        routes: [
            {
                path: '/login/pc/login',
                component: '@/pc/pages/login/index',
            },
            {
                path: '/login/pc/updatePassword',
                component: '@/pc/pages/updatePassword/index',
            },
            {
                path: '/login/pc/legalAgreement',
                component: '@/pc/pages/legalAgreement/index',
            },
            {
                path: '/login/pc/findPassword',
                component: '@/pc/pages/find-password/index',
            },
            {
                path: '/login/pc/secondValidate',
                component: '@/pc/pages/second-validate',
            },
            {
                path: '/login/pc/bindAccount',
                component: '@/pc/pages/bindAccount',
            },
        ],
    },
    {
        path: '/login/mobile',
        component: '@/mobile',
        routes: [
            {
                path: '/login/mobile/login',
                component: '@/mobile/pages/login',
            },
            {
                path: '/login/mobile/updatePassword',
                component: '@/mobile/pages/updatePassword',
            },
            {
                path: '/login/mobile/findPassword',
                component: '@/mobile/pages/findPassword',
            },
            {
                path: '/login/mobile/legalAgreement',
                component: '@/mobile/pages/legal-agreement',
            },
            {
                path: '/login/mobile/secondValidate',
                component: '@/mobile/pages/second-validate',
            },
        ],
    },
];

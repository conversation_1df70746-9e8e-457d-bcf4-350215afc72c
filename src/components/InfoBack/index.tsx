import { IconInformationFill } from '@streamax/poppy-icons';
import type { CSSProperties, ReactNode } from 'react';
import './index.less';

interface InfoBackProps {
    title: string | ReactNode;
    icon?: ReactNode;
    className?: string;
    style?: CSSProperties;
}

const InfoBack = (props: InfoBackProps) => {
    const { title, icon, className, style } = props;
    return (
        <div style={style} className={`info-back-containers ${className}`}>
            <div className="info-icon">
                <a>{icon ? icon : <IconInformationFill />}</a>
            </div>
            <div className="info-title">{title}</div>
        </div>
    );
};

export default InfoBack;

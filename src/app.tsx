// @ts-nocheck
import React from 'react';
import { I18nInit } from './i18n';
import { getRuntimeLocales } from '@/utils/common';
// @ts-ignore

export async function render(oldRender) {
    window.I18nInit = I18nInit;
    window.langueCache = {};

    // (async ()=>{
    //     const locales = await getRuntimeLocales('en_US', 1);
    //     const defaultLocales = require('./app.locale.json');
    //     window.I18nInit('en_US', { ...defaultLocales, ...locales });
    // })();
    oldRender();
}

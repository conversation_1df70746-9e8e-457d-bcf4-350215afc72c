# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/npm-debug.log*
/yarn-error.log
/package-lock.json
/resource-script/resource.sql
/i18n/result

# ide
.vscode
.history
.idea

# mock
mock/shentao.ts

# production
/dist
/install

# misc
.DS_Store

# umi
/src/.umi
/src/.umi-production
/src/.umi-test
/.env.local

.eslintcache
# sonar
.scannerwork

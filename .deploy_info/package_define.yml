service-dependencies:
  - nginx
  - assets-engine
cluster-variables:
  - name: web.public.port
    type: port
    port-type: listen
    desc: 系统监听端口
    default: 20603

  - name: static.intranet.addrs
    type: addrs
    desc: 静态资源服务集群内网地址
    default: multi_addrs(assets-engine,engine_port)

  - name: static.public.url
    type: string
    desc: 静态资源服务公网地址(https?://ip:port)
    default: https://static.streamax.com:20602
  
  - name: gateway.public.url
    type: string
    desc: 网关服务公网地址(https?://ip:port)
    default: https://ftcloud.streamax.com:20501

  - name: password.rsa2048.public.key
    type: string
    desc: 密码加密公钥
    default: -----BEGIN%20PUBLIC%20KEY-----%0AMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0VIjjNNY93vCmjuGqTteFHdt5rDFtca5%0A6v9oTFXormfQRTgjPtsr5tzCvpThcC8eY4UgeJfQT3zGcDnaKAONJQKHD2Q2w12RraazY0WDZcro%0AoqoLGx8hImpuGLOxc9ttXFDftDskrmcKNb8c20d9tB1v8qVE%2FpwKQF%2F8dfzI7sbgGkSZkCfuXHJN%0AtDRt5%2B97i6scaCMtwma3N7dy3bPg6GYlqGPrzcBdYjiaFfHUK82pEKSo7xK8%2Fe80wtUvwjuRSMva%0AD%2BPcX%2FnoSdiGO4oN3JqPioGnwv6DjntktbudJ9iayK0sWl%2FDZ%2Fqf90GQwcpH6Snigo8LymZOFD4%2F%0A4BaFgwIDAQAB%0A-----END%20PUBLIC%20KEY-----

  - name: find.password.areacode
    type: string
    desc: 找回密码时，手机号默认区号
    default: "86"

  - name: content.security.policy
    type: string
    desc: 是否开启CSP安全策略
    default: "0"

  - name: custom.login.bg.video.url
    type: string
    desc: 登录页背景视频地址
    default: "0"

  - name: custom.login.bg.img.url
    type: string
    desc: 登录页背景图片地址
    default: "0"

  - name: system.component.style
    type: string
    desc: 海外风格开关
    default: 'default'